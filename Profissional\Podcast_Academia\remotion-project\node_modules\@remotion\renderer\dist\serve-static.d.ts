import type { DownloadMap } from './assets/download-map';
import type { Compositor } from './compositor/compositor';
import type { LogLevel } from './log-level';
export declare const serveStatic: (path: string | null, options: {
    port: number | null;
    downloadMap: DownloadMap;
    remotionRoot: string;
    offthreadVideoThreads: number;
    logLevel: LogLevel;
    indent: boolean;
    offthreadVideoCacheSizeInBytes: number | null;
    binariesDirectory: string | null;
    forceIPv4: boolean;
}) => Promise<{
    port: number;
    close: () => Promise<void>;
    compositor: Compositor;
}>;
