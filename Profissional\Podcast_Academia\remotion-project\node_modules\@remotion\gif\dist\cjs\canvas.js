"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Canvas = void 0;
const jsx_runtime_1 = require("react/jsx-runtime");
/* eslint-disable react/require-default-props */
const react_1 = require("react");
const use_element_size_1 = require("./use-element-size");
const calcArgs = (fit, frameSize, canvasSize) => {
    switch (fit) {
        case 'fill': {
            return [
                0,
                0,
                frameSize.width,
                frameSize.height,
                0,
                0,
                canvasSize.width,
                canvasSize.height,
            ];
        }
        case 'contain': {
            const ratio = Math.min(canvasSize.width / frameSize.width, canvasSize.height / frameSize.height);
            const centerX = (canvasSize.width - frameSize.width * ratio) / 2;
            const centerY = (canvasSize.height - frameSize.height * ratio) / 2;
            return [
                0,
                0,
                frameSize.width,
                frameSize.height,
                centerX,
                centerY,
                frameSize.width * ratio,
                frameSize.height * ratio,
            ];
        }
        case 'cover': {
            const ratio = Math.max(canvasSize.width / frameSize.width, canvasSize.height / frameSize.height);
            const centerX = (canvasSize.width - frameSize.width * ratio) / 2;
            const centerY = (canvasSize.height - frameSize.height * ratio) / 2;
            return [
                0,
                0,
                frameSize.width,
                frameSize.height,
                centerX,
                centerY,
                frameSize.width * ratio,
                frameSize.height * ratio,
            ];
        }
        default:
            throw new Error('Unknown fit: ' + fit);
    }
};
const makeCanvas = () => {
    if (typeof document === 'undefined') {
        return null;
    }
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = 0;
    canvas.height = 0;
    return ctx;
};
exports.Canvas = (0, react_1.forwardRef)(({ index, frames, width, height, fit, className, style }, ref) => {
    const canvasRef = (0, react_1.useRef)(null);
    const [tempCtx] = (0, react_1.useState)(() => {
        return makeCanvas();
    });
    const size = (0, use_element_size_1.useElementSize)(canvasRef);
    (0, react_1.useImperativeHandle)(ref, () => {
        return canvasRef.current;
    }, []);
    (0, react_1.useEffect)(() => {
        var _a;
        if (!size) {
            return;
        }
        const imageData = frames[index];
        const ctx = (_a = canvasRef.current) === null || _a === void 0 ? void 0 : _a.getContext('2d');
        if (imageData && tempCtx && ctx) {
            if (tempCtx.canvas.width < imageData.width ||
                tempCtx.canvas.height < imageData.height) {
                tempCtx.canvas.width = imageData.width;
                tempCtx.canvas.height = imageData.height;
            }
            if (size.width > 0 && size.height > 0) {
                ctx.clearRect(0, 0, size.width, size.height);
                tempCtx.clearRect(0, 0, tempCtx.canvas.width, tempCtx.canvas.height);
            }
            tempCtx.putImageData(imageData, 0, 0);
            ctx.drawImage(tempCtx.canvas, ...calcArgs(fit, imageData, { width: size.width, height: size.height }));
        }
    }, [index, frames, fit, tempCtx, size]);
    return ((0, jsx_runtime_1.jsx)("canvas", { ref: canvasRef, className: className, style: style, width: width !== null && width !== void 0 ? width : size === null || size === void 0 ? void 0 : size.width, height: height !== null && height !== void 0 ? height : size === null || size === void 0 ? void 0 : size.height }));
});
