"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.noise4D = exports.noise3D = exports.noise2D = void 0;
const remotion_1 = require("remotion");
const simplex_noise_1 = require("simplex-noise");
const seedCache2d = new Map();
const seedCache3d = new Map();
const seedCache4d = new Map();
const generate2DNoise = (seed) => {
    const cached = seedCache2d.get(seed);
    if (cached) {
        return cached;
    }
    // If the cache is getting to big, remove entries based on FIFO principle
    if (seedCache2d.size > 10) {
        seedCache2d.delete(seedCache2d.keys().next().value);
    }
    const noise = (0, simplex_noise_1.createNoise2D)(() => (0, remotion_1.random)(seed));
    seedCache2d.set(seed, noise);
    return noise;
};
const generate3DNoise = (seed) => {
    const cached = seedCache3d.get(seed);
    if (cached) {
        return cached;
    }
    // If the cache is getting to big, remove entries based on FIFO principle
    if (seedCache3d.size > 10) {
        seedCache3d.delete(seedCache3d.keys().next().value);
    }
    const noise = (0, simplex_noise_1.createNoise3D)(() => (0, remotion_1.random)(seed));
    seedCache3d.set(seed, noise);
    return noise;
};
const generate4DNoise = (seed) => {
    const cached = seedCache4d.get(seed);
    if (cached) {
        return cached;
    }
    // If the cache is getting to big, remove entries based on FIFO principle
    if (seedCache4d.size > 10) {
        seedCache4d.delete(seedCache4d.keys().next().value);
    }
    const noise = (0, simplex_noise_1.createNoise4D)(() => (0, remotion_1.random)(seed));
    seedCache4d.set(seed, noise);
    return noise;
};
/*
 * @description Creates 2D noise.
 * @see [Documentation](https://www.remotion.dev/docs/noise/noise-2d)
 */
const noise2D = (seed, x, y) => {
    return generate2DNoise(seed)(x, y);
};
exports.noise2D = noise2D;
/*
 * @description Creates 3D noise.
 * @see [Documentation](https://www.remotion.dev/docs/noise/noise-3d)
 */
const noise3D = (seed, x, y, z) => generate3DNoise((0, remotion_1.random)(seed))(x, y, z);
exports.noise3D = noise3D;
/*
 * @description Creates 4D noise.
 * @see [Documentation](https://www.remotion.dev/docs/noise/noise-4d)
 */
const noise4D = (seed, x, y, z, w) => generate4DNoise((0, remotion_1.random)(seed))(x, y, z, w);
exports.noise4D = noise4D;
