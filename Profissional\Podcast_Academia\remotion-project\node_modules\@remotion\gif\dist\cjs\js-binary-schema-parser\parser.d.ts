import type { ParsedGif } from '../gifuct/types';
import type { Stream } from './uint8-parser';
export declare const parse: <T extends Record<string, T>>(stream: Stream, schema: GifSchema, result?: T, parent?: T) => GifSchema;
export declare const loop: <R>(schema: GifSchema, continueFunc: (st: Stream, r: R, p: R) => boolean) => (stream: Stream, result: R, parent: R, _parse: ParseFn<R>) => R[];
type ConditionalFunction<T> = (st: Stream, result: T, parent: T) => boolean;
type ParseFn<T> = (st: Stream, schema: GifSchema, result: T, parent: T) => void;
export type GifSchema = unknown | ParsedGif;
export declare const conditional: <T>(schema: GifSchema, conditionFunc: ConditionalFunction<T>) => (stream: Stream, result: T, parent: T, parseFn: ParseFn<T>) => void;
export {};
