{"repository": {"url": "https://github.com/remotion-dev/remotion/tree/main/packages/paths"}, "name": "@remotion/paths", "version": "4.0.324", "description": "Utilities for working with SVG paths", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/esm/index.mjs", "sideEffects": false, "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/remotion-dev/remotion/issues"}, "keywords": ["svg", "path", "utilities"], "devDependencies": {"eslint": "9.19.0", "@remotion/eslint-config-internal": "4.0.324"}, "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "module": "./dist/esm/index.mjs", "import": "./dist/esm/index.mjs", "require": "./dist/index.js"}}, "publishConfig": {"access": "public"}, "homepage": "https://www.remotion.dev/paths", "scripts": {"formatting": "prettier --experimental-cli src --check", "lint": "eslint src", "test": "bun test src", "make": "tsc -d && bun --env-file=../.env.bundle bundle.ts"}}