"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.preloadGif = void 0;
const gif_cache_1 = require("./gif-cache");
const react_tools_1 = require("./react-tools");
const resolve_gif_source_1 = require("./resolve-gif-source");
/**
 * @description Returns an object with two entries: waitUntilDone() that returns a Promise which can be awaited and free() which will cancel preloading or free up the memory if the GIF is not being used anymore.
 * @see [Documentation](https://www.remotion.dev/docs/gif/preload-gif)
 */
const preloadGif = (src) => {
    const resolvedSrc = (0, resolve_gif_source_1.resolveGifSource)(src);
    if (gif_cache_1.volatileGifCache.has(resolvedSrc)) {
        return {
            waitUntilDone: () => Promise.resolve(),
            free: () => gif_cache_1.volatileGifCache.delete(resolvedSrc),
        };
    }
    if (gif_cache_1.manuallyManagedGifCache.has(resolvedSrc)) {
        return {
            waitUntilDone: () => Promise.resolve(),
            free: () => gif_cache_1.manuallyManagedGifCache.delete(resolvedSrc),
        };
    }
    const { prom, cancel } = (0, react_tools_1.parseWithWorker)(resolvedSrc);
    let deleted = false;
    prom.then((p) => {
        if (!deleted) {
            gif_cache_1.manuallyManagedGifCache.set(resolvedSrc, p);
        }
    });
    return {
        waitUntilDone: () => prom.then(() => undefined),
        free: () => {
            cancel();
            deleted = true;
            gif_cache_1.manuallyManagedGifCache.delete(resolvedSrc);
        },
    };
};
exports.preloadGif = preloadGif;
