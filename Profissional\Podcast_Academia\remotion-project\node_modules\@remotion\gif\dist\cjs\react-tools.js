"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseWithWorker = exports.parseGif = void 0;
const parse_generate_1 = require("./parse-generate");
const worker_1 = require("./worker");
const parseGif = async ({ src, controller, }) => {
    const raw = await (0, parse_generate_1.parse)(src, { signal: controller.signal });
    return (0, parse_generate_1.generate)(raw);
};
exports.parseGif = parseGif;
const parseWithWorker = (src) => {
    const worker = (0, worker_1.makeWorker)();
    let handler = null;
    const prom = new Promise((resolve, reject) => {
        handler = (e) => {
            const message = e.data || e;
            if (message.src === src) {
                if (message.error) {
                    reject(new Error(message.error));
                }
                else {
                    const data = message.error ? message : (0, parse_generate_1.generate)(message);
                    resolve(data);
                    worker.terminate();
                }
            }
        };
        worker.addEventListener('message', handler);
        worker.postMessage({ src, type: 'parse' });
    });
    return {
        prom,
        cancel: () => {
            worker.postMessage({ src, type: 'cancel' });
            worker.removeEventListener('message', handler);
            worker.terminate();
        },
    };
};
exports.parseWithWorker = parseWithWorker;
