{"name": "remotion-animated", "version": "2.2.0", "description": "🌟 A delightful way to animate objects in Remotion.", "license": "MIT", "homepage": "https://www.remotion-animated.dev", "repository": {"type": "git", "url": "https://github.com/stefanwittwer/remotion-animated"}, "keywords": ["remotion", "react", "video", "animation", "react video"], "publishConfig": {"access": "public"}, "scripts": {"prepublish": "rm -f ./README.md && cp ../../README.md ./README.md", "build": "tsc -d && NODE_ENV=production bun bundle.ts", "watch": "tsc -w", "lint": "eslint *.ts*"}, "files": ["lib", "dist", "es"], "source": "src/index.tsx", "module": "dist/esm/index.mjs", "main": "dist/index.js", "types": "dist/index.d.ts", "devDependencies": {"@types/bun": "^1.1.1", "@types/react": "^18.2.18", "@types/react-dom": "^18.2.7", "eslint": "^8.46.0", "eslint-config-custom": "*", "react": "^18.2.0", "remotion": "^4.0.267", "tsconfig": "*", "typescript": "^5.1.6"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0", "remotion": ">=4.0.0"}}