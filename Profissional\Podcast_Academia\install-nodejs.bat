@echo off
echo 🚀 Instalando Node.js...
echo.

REM Tentar instalar via winget
echo Tentando instalar via winget...
winget install --id OpenJS.NodeJS --exact --source winget --accept-source-agreements --accept-package-agreements
if %errorlevel% == 0 (
    echo ✅ Node.js instalado via winget
    goto :refresh_path
)

REM Tentar instalar via chocolatey
echo Tentando instalar via chocolatey...
choco install nodejs -y
if %errorlevel% == 0 (
    echo ✅ Node.js instalado via chocolatey
    goto :refresh_path
)

REM Tentar download direto
echo Fazendo download direto...
powershell -Command "Invoke-WebRequest -Uri 'https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi' -OutFile 'nodejs-installer.msi'"
if exist nodejs-installer.msi (
    echo Instalando Node.js...
    msiexec /i nodejs-installer.msi /quiet
    echo ✅ Node.js instalado via download direto
    del nodejs-installer.msi
)

:refresh_path
echo.
echo 🔄 Atualizando PATH...
refreshenv
call refreshenv

echo.
echo 🧪 Testando instalação...
node --version
npm --version

echo.
echo ✅ Instalação concluída!
pause
