import { Easing as RemotionEasing } from 'remotion';
import CustomEasing from './CustomEasing';
var Easing;
(function (Easing) {
    Easing.Linear = CustomEasing(RemotionEasing.linear);
    Easing.Bounce = CustomEasing(RemotionEasing.bounce);
    Easing.Circle = CustomEasing(RemotionEasing.circle);
    Easing.Quad = CustomEasing(RemotionEasing.quad);
    Easing.Cubic = CustomEasing(RemotionEasing.cubic);
    Easing.Quint = CustomEasing(RemotionEasing.poly(5));
    Easing.Exponential = CustomEasing(RemotionEasing.exp);
    Easing.Sinusoidal = CustomEasing(RemotionEasing.sin);
    Easing.Elastic = (bounciness) => CustomEasing(RemotionEasing.elastic(bounciness));
    Easing.Custom = (x1, y1, x2, y2) => CustomEasing(RemotionEasing.bezier(x1, y1, x2, y2));
})(Easing || (Easing = {}));
export default Easing;
