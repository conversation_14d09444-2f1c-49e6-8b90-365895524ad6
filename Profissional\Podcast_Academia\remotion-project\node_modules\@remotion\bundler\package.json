{"repository": {"url": "https://github.com/remotion-dev/remotion/tree/main/packages/bundler"}, "name": "@remotion/bundler", "version": "4.0.324", "description": "Bundle Remotion compositions using Webpack", "main": "dist/index.js", "sideEffects": false, "bugs": {"url": "https://github.com/remotion-dev/remotion/issues"}, "files": ["dist", "react-shim.js", "renderEntry.tsx", "favicon.ico"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"css-loader": "5.2.7", "esbuild": "0.25.0", "react-refresh": "0.9.0", "style-loader": "4.0.0", "source-map": "0.7.3", "webpack": "5.96.1", "remotion": "4.0.324", "@remotion/studio": "4.0.324", "@remotion/studio-shared": "4.0.324", "@remotion/media-parser": "4.0.324"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "devDependencies": {"react": "19.0.0", "react-dom": "19.0.0", "eslint": "9.19.0", "@remotion/eslint-config-internal": "4.0.324"}, "keywords": ["remotion", "ffmpeg", "video", "react", "webpack", "player"], "publishConfig": {"access": "public"}, "homepage": "https://www.remotion.dev/docs/bundler", "scripts": {"formatting": "prettier --experimental-cli src --check", "lint": "eslint src", "test": "bun test src", "make": "tsc -d"}}