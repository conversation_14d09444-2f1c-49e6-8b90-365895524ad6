"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLottieFrame = void 0;
const getLottieFrame = ({ currentFrame, direction, loop, totalFrames, }) => {
    const nextFrame = loop
        ? currentFrame % totalFrames
        : Math.min(currentFrame, totalFrames - 1);
    if (direction === 'backward') {
        return totalFrames - nextFrame;
    }
    return nextFrame;
};
exports.getLottieFrame = getLottieFrame;
