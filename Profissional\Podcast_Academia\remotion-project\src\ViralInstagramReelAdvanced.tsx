import React from "react";
import {
  AbsoluteFill,
  Video,
  Audio,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  staticFile,
  Sequence,
  spring,
  Img,
} from "remotion";

interface ViralInstagramReelAdvancedProps {
  videoPath: string;
  audioPath: string;
  startTime: number;
  duration: number;
  title: string;
  subtitle: string;
  hook: string;
  cta: string;
  author: string;
}

export const ViralInstagramReelAdvanced: React.FC<ViralInstagramReelAdvancedProps> = ({
  videoPath,
  audioPath,
  startTime,
  duration,
  title,
  subtitle,
  hook,
  cta,
  author,
}) => {
  const frame = useCurrentFrame();
  const { durationInFrames, fps } = useVideoConfig();

  // Configurações de timing viral avançado
  const hookDuration = 3 * fps; // 3 segundos para hook
  const mainContentStart = hookDuration;
  const ctaDuration = 2 * fps; // 2 segundos para CTA
  const ctaStart = durationInFrames - ctaDuration;

  // Momentos específicos para imagens (baseado em palavras-chave)
  const moneyImageStart = 5 * fps; // "25 mil" aparece
  const moneyImageEnd = 8 * fps;
  
  const clientImageStart = 15 * fps; // "primeiro cliente" aparece
  const clientImageEnd = 18 * fps;
  
  const aiImageStart = 25 * fps; // "IA" aparece
  const aiImageEnd = 28 * fps;

  // Animações baseadas em padrões virais
  const hookScale = spring({
    frame,
    fps,
    config: {
      damping: 200,
      stiffness: 100,
    },
  });

  const hookOpacity = interpolate(
    frame,
    [0, 15, hookDuration - 15, hookDuration],
    [0, 1, 1, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  const mainContentOpacity = interpolate(
    frame,
    [mainContentStart, mainContentStart + 15, ctaStart - 15, ctaStart],
    [0, 1, 1, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  const ctaScale = spring({
    frame: frame - ctaStart,
    fps,
    config: {
      damping: 100,
      stiffness: 200,
    },
  });

  const ctaOpacity = interpolate(
    frame,
    [ctaStart, ctaStart + 15, durationInFrames - 5, durationInFrames],
    [0, 1, 1, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  // Animações para imagens dinâmicas
  const moneyImageOpacity = interpolate(
    frame,
    [moneyImageStart, moneyImageStart + 10, moneyImageEnd - 10, moneyImageEnd],
    [0, 1, 1, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  const clientImageOpacity = interpolate(
    frame,
    [clientImageStart, clientImageStart + 10, clientImageEnd - 10, clientImageEnd],
    [0, 1, 1, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  const aiImageOpacity = interpolate(
    frame,
    [aiImageStart, aiImageStart + 10, aiImageEnd - 10, aiImageEnd],
    [0, 1, 1, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  // Progress bar viral
  const progress = interpolate(
    frame,
    [0, durationInFrames],
    [0, 100],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  // Efeito de zoom no vídeo para criar dinamismo
  const videoZoom = interpolate(
    frame,
    [0, durationInFrames],
    [1.0, 1.1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  return (
    <AbsoluteFill>
      {/* Áudio sincronizado */}
      <Audio
        src={staticFile(audioPath)}
        startFrom={0}
        endAt={duration * fps}
        volume={0.9}
      />

      {/* Vídeo principal com crop para 9:16 e zoom dinâmico */}
      <div
        style={{
          width: "100%",
          height: "100%",
          background: "#000",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          overflow: "hidden",
        }}
      >
        <Video
          src={staticFile(videoPath)}
          startFrom={startTime * fps}
          endAt={(startTime + duration) * fps}
          style={{
            width: "100%",
            height: "133.33%", // Crop 16:9 para 9:16
            objectFit: "cover",
            objectPosition: "center",
            transform: `scale(${videoZoom})`,
            transition: "transform 0.1s ease-out",
          }}
          muted={true}
        />
      </div>

      {/* Overlay escuro para melhor legibilidade */}
      <AbsoluteFill
        style={{
          background: "linear-gradient(180deg, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 50%, rgba(0,0,0,0.4) 100%)",
        }}
      />

      {/* Hook viral - primeiros 3 segundos */}
      {frame < hookDuration && (
        <AbsoluteFill
          style={{
            background: "linear-gradient(45deg, rgba(0,0,0,0.8), rgba(0,0,0,0.4))",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            opacity: hookOpacity,
          }}
        >
          <div
            style={{
              transform: `scale(${hookScale})`,
              textAlign: "center",
              padding: "40px",
            }}
          >
            <h1
              style={{
                fontSize: "4rem",
                fontWeight: "900",
                color: "#fff",
                margin: "0 0 20px 0",
                fontFamily: "'Inter', -apple-system, sans-serif",
                textShadow: "0 4px 20px rgba(0,0,0,0.8)",
                lineHeight: "1.1",
                letterSpacing: "-0.02em",
                background: "linear-gradient(45deg, #ff6b35, #f7931e)",
                backgroundClip: "text",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
              }}
            >
              {hook}
            </h1>
            <div
              style={{
                width: "120px",
                height: "6px",
                background: "linear-gradient(90deg, #ff6b35, #f7931e)",
                margin: "0 auto",
                borderRadius: "3px",
                boxShadow: "0 4px 15px rgba(255,107,53,0.4)",
              }}
            />
          </div>
        </AbsoluteFill>
      )}

      {/* Conteúdo principal com elementos dinâmicos */}
      {frame >= mainContentStart && frame < ctaStart && (
        <>
          {/* Título e subtítulo */}
          <div
            style={{
              position: "absolute",
              top: "80px",
              left: "30px",
              right: "30px",
              opacity: mainContentOpacity,
              zIndex: 10,
            }}
          >
            <h2
              style={{
                fontSize: "2.2rem",
                fontWeight: "800",
                color: "#fff",
                margin: "0 0 15px 0",
                fontFamily: "'Inter', -apple-system, sans-serif",
                textShadow: "0 3px 15px rgba(0,0,0,0.9)",
                lineHeight: "1.2",
                background: "rgba(0,0,0,0.6)",
                padding: "15px 20px",
                borderRadius: "15px",
                border: "2px solid rgba(255,107,53,0.3)",
              }}
            >
              {title}
            </h2>
            <p
              style={{
                fontSize: "1.3rem",
                fontWeight: "500",
                color: "#f0f0f0",
                margin: "0",
                fontFamily: "'Inter', -apple-system, sans-serif",
                textShadow: "0 2px 10px rgba(0,0,0,0.8)",
                background: "rgba(0,0,0,0.5)",
                padding: "12px 20px",
                borderRadius: "12px",
              }}
            >
              {subtitle}
            </p>
          </div>

          {/* Branding profissional */}
          <div
            style={{
              position: "absolute",
              top: "40px",
              right: "30px",
              padding: "12px 20px",
              background: "linear-gradient(135deg, rgba(255,107,53,0.9), rgba(247,147,30,0.9))",
              borderRadius: "25px",
              border: "2px solid rgba(255,255,255,0.3)",
              opacity: mainContentOpacity,
              boxShadow: "0 8px 25px rgba(0,0,0,0.3)",
            }}
          >
            <span
              style={{
                fontSize: "1rem",
                fontWeight: "700",
                color: "#fff",
                fontFamily: "'Inter', -apple-system, sans-serif",
                textShadow: "0 1px 3px rgba(0,0,0,0.3)",
              }}
            >
              @{author}
            </span>
          </div>
        </>
      )}

      {/* Imagem dinâmica: Dinheiro (R$ 25 mil) */}
      {frame >= moneyImageStart && frame <= moneyImageEnd && (
        <div
          style={{
            position: "absolute",
            bottom: "200px",
            right: "30px",
            width: "120px",
            height: "120px",
            opacity: moneyImageOpacity,
            transform: `scale(${spring({
              frame: frame - moneyImageStart,
              fps,
              config: { damping: 200, stiffness: 100 }
            })})`,
            zIndex: 15,
          }}
        >
          <div
            style={{
              width: "100%",
              height: "100%",
              borderRadius: "20px",
              overflow: "hidden",
              border: "3px solid #ff6b35",
              boxShadow: "0 10px 30px rgba(255,107,53,0.4)",
              background: "#fff",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <span style={{ fontSize: "3rem" }}>💰</span>
          </div>
        </div>
      )}

      {/* Imagem dinâmica: Cliente/Handshake */}
      {frame >= clientImageStart && frame <= clientImageEnd && (
        <div
          style={{
            position: "absolute",
            bottom: "200px",
            left: "30px",
            width: "120px",
            height: "120px",
            opacity: clientImageOpacity,
            transform: `scale(${spring({
              frame: frame - clientImageStart,
              fps,
              config: { damping: 200, stiffness: 100 }
            })})`,
            zIndex: 15,
          }}
        >
          <div
            style={{
              width: "100%",
              height: "100%",
              borderRadius: "20px",
              overflow: "hidden",
              border: "3px solid #f7931e",
              boxShadow: "0 10px 30px rgba(247,147,30,0.4)",
              background: "#fff",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <span style={{ fontSize: "3rem" }}>🤝</span>
          </div>
        </div>
      )}

      {/* Imagem dinâmica: IA */}
      {frame >= aiImageStart && frame <= aiImageEnd && (
        <div
          style={{
            position: "absolute",
            top: "300px",
            right: "30px",
            width: "120px",
            height: "120px",
            opacity: aiImageOpacity,
            transform: `scale(${spring({
              frame: frame - aiImageStart,
              fps,
              config: { damping: 200, stiffness: 100 }
            })})`,
            zIndex: 15,
          }}
        >
          <div
            style={{
              width: "100%",
              height: "100%",
              borderRadius: "20px",
              overflow: "hidden",
              border: "3px solid #2E86AB",
              boxShadow: "0 10px 30px rgba(46,134,171,0.4)",
              background: "#fff",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <span style={{ fontSize: "3rem" }}>🤖</span>
          </div>
        </div>
      )}

      {/* CTA viral - últimos 2 segundos */}
      {frame >= ctaStart && (
        <AbsoluteFill
          style={{
            background: "linear-gradient(135deg, rgba(255,107,53,0.95), rgba(247,147,30,0.95))",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            opacity: ctaOpacity,
          }}
        >
          <div
            style={{
              transform: `scale(${ctaScale})`,
              textAlign: "center",
              padding: "40px",
            }}
          >
            <h1
              style={{
                fontSize: "3.2rem",
                fontWeight: "900",
                color: "#fff",
                margin: "0 0 30px 0",
                fontFamily: "'Inter', -apple-system, sans-serif",
                textShadow: "0 4px 20px rgba(0,0,0,0.3)",
                lineHeight: "1.1",
              }}
            >
              {cta}
            </h1>
            <div
              style={{
                padding: "18px 35px",
                background: "#fff",
                borderRadius: "35px",
                display: "inline-block",
                boxShadow: "0 10px 30px rgba(0,0,0,0.3)",
                border: "3px solid rgba(255,107,53,0.3)",
              }}
            >
              <span
                style={{
                  fontSize: "1.4rem",
                  fontWeight: "800",
                  color: "#ff6b35",
                  fontFamily: "'Inter', -apple-system, sans-serif",
                }}
              >
                Saiba mais →
              </span>
            </div>
          </div>
        </AbsoluteFill>
      )}

      {/* Progress bar minimalista */}
      <div
        style={{
          position: "absolute",
          bottom: "20px",
          left: "30px",
          right: "30px",
          height: "4px",
          background: "rgba(255,255,255,0.3)",
          borderRadius: "2px",
          overflow: "hidden",
          zIndex: 20,
        }}
      >
        <div
          style={{
            width: `${progress}%`,
            height: "100%",
            background: "linear-gradient(90deg, #ff6b35, #f7931e)",
            borderRadius: "2px",
            transition: "width 0.1s ease-out",
            boxShadow: "0 0 10px rgba(255,107,53,0.5)",
          }}
        />
      </div>

      {/* Elementos de engajamento aprimorados */}
      <div
        style={{
          position: "absolute",
          bottom: "100px",
          right: "30px",
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          opacity: mainContentOpacity,
          zIndex: 15,
        }}
      >
        {/* Like */}
        <div
          style={{
            width: "60px",
            height: "60px",
            background: "rgba(255,255,255,0.15)",
            borderRadius: "30px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            border: "2px solid rgba(255,255,255,0.3)",
            backdropFilter: "blur(10px)",
            boxShadow: "0 8px 25px rgba(0,0,0,0.2)",
          }}
        >
          <span style={{ fontSize: "1.8rem" }}>❤️</span>
        </div>
        
        {/* Comment */}
        <div
          style={{
            width: "60px",
            height: "60px",
            background: "rgba(255,255,255,0.15)",
            borderRadius: "30px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            border: "2px solid rgba(255,255,255,0.3)",
            backdropFilter: "blur(10px)",
            boxShadow: "0 8px 25px rgba(0,0,0,0.2)",
          }}
        >
          <span style={{ fontSize: "1.8rem" }}>💬</span>
        </div>
        
        {/* Share */}
        <div
          style={{
            width: "60px",
            height: "60px",
            background: "rgba(255,255,255,0.15)",
            borderRadius: "30px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            border: "2px solid rgba(255,255,255,0.3)",
            backdropFilter: "blur(10px)",
            boxShadow: "0 8px 25px rgba(0,0,0,0.2)",
          }}
        >
          <span style={{ fontSize: "1.8rem" }}>📤</span>
        </div>
      </div>
    </AbsoluteFill>
  );
};
