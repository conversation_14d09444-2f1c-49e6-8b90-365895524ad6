# @remotion/preload
 
Preloads assets for use in Remotion
 
[![NPM Downloads](https://img.shields.io/npm/dm/@remotion/preload.svg?style=flat&color=black&label=Downloads)](https://npmcharts.com/compare/@remotion/preload?minimal=true)
 
## Installation
 
```bash
npm install @remotion/preload --save-exact
```
 
When installing a Remotion package, make sure to align the version of all `remotion` and `@remotion/*` packages to the same version.
Remove the `^` character from the version number to use the exact version.
 
## Usage
 
See the [documentation](https://www.remotion.dev/docs/preload) for more information.
