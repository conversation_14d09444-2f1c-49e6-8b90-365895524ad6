const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuração dos vídeos para processar
const videoConfigs = [
  {
    filename: "Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4",
    segments: [
      { start: 0, duration: 60, title: "Introdução aos Agentes IA", hook: "🤖 Construa seu primeiro Agente IA" },
      { start: 60, duration: 60, title: "Fundamentos da IA", hook: "⚡ Aprenda os fundamentos" },
      { start: 120, duration: 60, title: "Primeiros Passos", hook: "🚀 Comece agora mesmo" },
      { start: 180, duration: 60, title: "Estratégias Avançadas", hook: "💡 Estratégias que funcionam" },
      { start: 240, duration: 60, title: "Implementação Prática", hook: "🎯 Coloque em prática" },
    ]
  }
];

// Configuração das cores para diferentes segmentos
const colorSchemes = [
  {
    primary: "#FF6B35",
    secondary: "#2E86AB", 
    accent: "#A23B72",
    background: "#F18F01",
    text: "#FFFFFF"
  },
  {
    primary: "#6C5CE7",
    secondary: "#00CEC9",
    accent: "#FD79A8",
    background: "#A29BFE",
    text: "#FFFFFF"
  },
  {
    primary: "#00B894",
    secondary: "#0984E3",
    accent: "#E84393",
    background: "#55EFC4",
    text: "#FFFFFF"
  },
  {
    primary: "#FF7675",
    secondary: "#74B9FF",
    accent: "#FD79A8",
    background: "#FDCB6E",
    text: "#FFFFFF"
  }
];

// Função para renderizar um formato específico
async function renderFormat(format, props, outputPath) {
  const propsJson = JSON.stringify(props).replace(/"/g, '\\"');
  const remotionPath = path.join(__dirname, '..', 'node_modules', '.bin', 'remotion.cmd');
  const command = `"${remotionPath}" render src/index.ts ${format} "${outputPath}" --props="${propsJson}"`;
  
  console.log(`🎬 Renderizando ${format}: ${props.title}`);
  console.log(`📁 Output: ${outputPath}`);
  
  return new Promise((resolve, reject) => {
    exec(command, { cwd: path.join(__dirname, '..') }, (error, stdout, stderr) => {
      if (error) {
        console.error(`❌ Erro ao renderizar ${format}:`, error.message);
        resolve(false);
        return;
      }
      
      if (stderr) {
        console.log(`⚠️  Warnings: ${stderr}`);
      }
      
      console.log(`✅ ${format} renderizado com sucesso!`);
      resolve(true);
    });
  });
}

// Função principal
async function main() {
  console.log('🚀 Iniciando renderização em massa dos vídeos do podcast...\n');
  
  // Criar diretório de saída
  const outputDir = path.join(__dirname, '..', 'output');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  let totalRendered = 0;
  let successCount = 0;
  
  // Processar cada vídeo
  for (const [videoIndex, videoConfig] of videoConfigs.entries()) {
    console.log(`\n📺 Processando: ${videoConfig.filename}`);
    
    // Processar cada segmento
    for (const [segmentIndex, segment] of videoConfig.segments.entries()) {
      const colorScheme = colorSchemes[segmentIndex % colorSchemes.length];
      
      // Preparar props base
      const baseProps = {
        videoPath: `../${videoConfig.filename}`,
        startTime: segment.start,
        duration: segment.duration,
        title: segment.title,
        subtitle: segment.hook,
        author: "Sami",
        colors: colorScheme
      };
      
      // Renderizar Reel (60s)
      if (segment.duration >= 15) {
        const reelProps = { ...baseProps, duration: Math.min(segment.duration, 60) };
        const reelOutput = path.join(outputDir, `reel_${videoIndex}_${segmentIndex}_${segment.title.replace(/\s+/g, '_')}.mp4`);
        
        totalRendered++;
        const success = await renderFormat('InstagramReel', reelProps, reelOutput);
        if (success) successCount++;
      }
      
      // Renderizar Story (15s)
      const storyProps = { ...baseProps, duration: 15 };
      const storyOutput = path.join(outputDir, `story_${videoIndex}_${segmentIndex}_${segment.title.replace(/\s+/g, '_')}.mp4`);
      
      totalRendered++;
      const storySuccess = await renderFormat('InstagramStory', storyProps, storyOutput);
      if (storySuccess) successCount++;
      
      // Renderizar Post (60s)
      if (segment.duration >= 30) {
        const postProps = { ...baseProps, duration: Math.min(segment.duration, 60) };
        const postOutput = path.join(outputDir, `post_${videoIndex}_${segmentIndex}_${segment.title.replace(/\s+/g, '_')}.mp4`);
        
        totalRendered++;
        const postSuccess = await renderFormat('InstagramPost', postProps, postOutput);
        if (postSuccess) successCount++;
      }
    }
  }
  
  // Relatório final
  console.log('\n📊 RELATÓRIO FINAL:');
  console.log(`✅ Sucessos: ${successCount}/${totalRendered}`);
  console.log(`❌ Falhas: ${totalRendered - successCount}/${totalRendered}`);
  console.log(`📁 Arquivos salvos em: ${outputDir}`);
  
  // Gerar arquivo de índice
  generateIndexFile(outputDir);
  
  console.log('\n🎉 Renderização concluída!');
  console.log('💡 Próximos passos:');
  console.log('1. Revisar os vídeos gerados');
  console.log('2. Testar no Instagram');
  console.log('3. Ajustar cores/timing se necessário');
  console.log('4. Programar posts para horários de pico');
}

// Função para gerar arquivo de índice
function generateIndexFile(outputDir) {
  const files = fs.readdirSync(outputDir).filter(file => file.endsWith('.mp4'));
  
  const indexContent = {
    generated: new Date().toISOString(),
    total_videos: files.length,
    formats: {
      reels: files.filter(f => f.startsWith('reel_')).length,
      stories: files.filter(f => f.startsWith('story_')).length,
      posts: files.filter(f => f.startsWith('post_')).length
    },
    files: files.map(file => ({
      filename: file,
      format: file.split('_')[0],
      size: fs.existsSync(path.join(outputDir, file)) ? fs.statSync(path.join(outputDir, file)).size : 0,
      created: fs.existsSync(path.join(outputDir, file)) ? fs.statSync(path.join(outputDir, file)).mtime : new Date()
    })),
    instagram_specs: {
      reel: { resolution: "1080x1920", max_duration: "60s", format: "9:16" },
      story: { resolution: "1080x1920", max_duration: "15s", format: "9:16" },
      post: { resolution: "1080x1080", max_duration: "60s", format: "1:1" }
    }
  };
  
  fs.writeFileSync(
    path.join(outputDir, 'index.json'),
    JSON.stringify(indexContent, null, 2)
  );
  
  console.log('📋 Arquivo de índice gerado: index.json');
}

// Executar se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, renderFormat };
