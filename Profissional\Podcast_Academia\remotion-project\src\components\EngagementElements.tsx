import React from "react";
import {
  AbsoluteFill,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  spring,
} from "remotion";

interface EngagementElementsProps {
  frame: number;
  fps: number;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
  author: string;
}

export const EngagementElements: React.FC<EngagementElementsProps> = ({
  frame,
  fps,
  colors,
  author
}) => {
  // Animações para diferentes elementos
  const likeAnimation = spring({
    frame: frame % (fps * 6), // Repete a cada 6 segundos
    fps,
    config: {
      damping: 10,
      stiffness: 100,
    }
  });
  
  const shareAnimation = spring({
    frame: (frame + fps * 2) % (fps * 8), // Deslocado 2 segundos, repete a cada 8 segundos
    fps,
    config: {
      damping: 15,
      stiffness: 80,
    }
  });
  
  const commentAnimation = spring({
    frame: (frame + fps * 4) % (fps * 10), // Deslocado 4 segundos, repete a cada 10 segundos
    fps,
    config: {
      damping: 20,
      stiffness: 60,
    }
  });
  
  // Floating emoji animation - corrigido para evitar valores monotônicos
  const floatingCycle = frame % (fps * 4);
  const floatingEmoji = interpolate(
    floatingCycle,
    [0, fps * 4],
    [0, -200],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );
  
  const floatingOpacity = interpolate(
    floatingCycle,
    [0, fps * 1, fps * 3, fps * 4],
    [0, 1, 1, 0],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );
  
  // Progress bar calculation
  const totalDuration = fps * 60; // 60 segundos
  const progressPercentage = (frame / totalDuration) * 100;
  
  return (
    <AbsoluteFill>
      {/* Botão de Like animado */}
      <div
        style={{
          position: "absolute",
          right: 30,
          top: "30%",
          transform: `scale(${1 + likeAnimation * 0.3})`,
        }}
      >
        <div
          style={{
            width: 60,
            height: 60,
            borderRadius: "50%",
            background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            boxShadow: "0 10px 25px rgba(0,0,0,0.3)",
            border: `3px solid ${colors.text}`,
          }}
        >
          <span style={{ fontSize: 28 }}>❤️</span>
        </div>
        <div
          style={{
            color: colors.text,
            fontSize: 14,
            fontWeight: "bold",
            textAlign: "center",
            marginTop: 8,
            textShadow: "1px 1px 2px rgba(0,0,0,0.7)",
          }}
        >
          2.5K
        </div>
      </div>
      
      {/* Botão de Share animado */}
      <div
        style={{
          position: "absolute",
          right: 30,
          top: "45%",
          transform: `scale(${1 + shareAnimation * 0.2})`,
        }}
      >
        <div
          style={{
            width: 60,
            height: 60,
            borderRadius: "50%",
            background: `linear-gradient(135deg, ${colors.secondary}, ${colors.primary})`,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            boxShadow: "0 10px 25px rgba(0,0,0,0.3)",
            border: `3px solid ${colors.text}`,
          }}
        >
          <span style={{ fontSize: 28 }}>🚀</span>
        </div>
        <div
          style={{
            color: colors.text,
            fontSize: 14,
            fontWeight: "bold",
            textAlign: "center",
            marginTop: 8,
            textShadow: "1px 1px 2px rgba(0,0,0,0.7)",
          }}
        >
          847
        </div>
      </div>
      
      {/* Botão de Comment animado */}
      <div
        style={{
          position: "absolute",
          right: 30,
          top: "60%",
          transform: `scale(${1 + commentAnimation * 0.2})`,
        }}
      >
        <div
          style={{
            width: 60,
            height: 60,
            borderRadius: "50%",
            background: `linear-gradient(135deg, ${colors.accent}, ${colors.secondary})`,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            boxShadow: "0 10px 25px rgba(0,0,0,0.3)",
            border: `3px solid ${colors.text}`,
          }}
        >
          <span style={{ fontSize: 28 }}>💬</span>
        </div>
        <div
          style={{
            color: colors.text,
            fontSize: 14,
            fontWeight: "bold",
            textAlign: "center",
            marginTop: 8,
            textShadow: "1px 1px 2px rgba(0,0,0,0.7)",
          }}
        >
          156
        </div>
      </div>
      
      {/* Floating emojis */}
      <div
        style={{
          position: "absolute",
          left: 50,
          bottom: 400,
          transform: `translateY(${floatingEmoji}px)`,
          opacity: floatingOpacity,
          fontSize: 40,
        }}
      >
        🔥
      </div>
      
      <div
        style={{
          position: "absolute",
          left: 150,
          bottom: 350,
          transform: `translateY(${floatingEmoji - 50}px)`,
          opacity: floatingOpacity * 0.8,
          fontSize: 35,
        }}
      >
        ⚡
      </div>
      
      <div
        style={{
          position: "absolute",
          left: 100,
          bottom: 300,
          transform: `translateY(${floatingEmoji - 100}px)`,
          opacity: floatingOpacity * 0.6,
          fontSize: 30,
        }}
      >
        🎯
      </div>
      
      {/* Trending indicator */}
      <div
        style={{
          position: "absolute",
          top: 50,
          right: 40,
          background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,
          padding: "8px 16px",
          borderRadius: 20,
          border: `2px solid ${colors.text}`,
          boxShadow: "0 5px 15px rgba(0,0,0,0.3)",
        }}
      >
        <div
          style={{
            color: colors.text,
            fontSize: 16,
            fontWeight: "bold",
            textShadow: "1px 1px 2px rgba(0,0,0,0.5)",
          }}
        >
          🔥 TRENDING
        </div>
      </div>
      
      {/* Progress bar */}
      <div
        style={{
          position: "absolute",
          bottom: 20,
          left: 40,
          right: 40,
          height: 6,
          background: `${colors.text}30`,
          borderRadius: 3,
        }}
      >
        <div
          style={{
            height: "100%",
            background: `linear-gradient(90deg, ${colors.primary}, ${colors.accent})`,
            borderRadius: 3,
            width: `${Math.min(progressPercentage, 100)}%`,
            boxShadow: `0 0 10px ${colors.primary}`,
          }}
        />
      </div>
    </AbsoluteFill>
  );
};
