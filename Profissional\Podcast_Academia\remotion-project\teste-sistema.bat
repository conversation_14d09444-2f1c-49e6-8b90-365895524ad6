@echo off
echo 🎬 SISTEMA DE EDIÇÃO PROFISSIONAL PARA INSTAGRAM
echo ===============================================
echo.

cd /d "C:\Users\<USER>\Documents\Augment_Projects\Profissional\Podcast_Academia\remotion-project"

echo 📋 Verificando estrutura do projeto...
echo.

if exist "node_modules" (
    echo ✅ Node modules: OK
) else (
    echo ❌ Node modules: NOT FOUND
    goto :error
)

if exist "src\index.ts" (
    echo ✅ Arquivo principal: OK
) else (
    echo ❌ Arquivo principal: NOT FOUND
    goto :error
)

if exist "..\Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4" (
    echo ✅ Vídeo de origem: OK
) else (
    echo ❌ Vídeo de origem: NOT FOUND
    goto :error
)

echo.
echo 🚀 Iniciando renderização de teste...
echo.

rem Criar diretório de saída
if not exist "output" mkdir "output"

rem Executar renderização
echo 🎥 Renderizando Instagram Story...
node_modules\.bin\remotion.cmd render src/index.ts InstagramStory "output/teste_story.mp4" --props="{\"videoPath\": \"../Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4\", \"startTime\": 0, \"duration\": 15, \"title\": \"Como construir seu primeiro Agente IA\", \"author\": \"Sami\", \"colors\": {\"primary\": \"#FF6B35\", \"secondary\": \"#2E86AB\", \"accent\": \"#A23B72\", \"background\": \"#F18F01\", \"text\": \"#FFFFFF\"}}"

if %errorlevel% == 0 (
    echo.
    echo ✅ SUCESSO! Vídeo renderizado com sucesso!
    echo 📁 Arquivo: output\teste_story.mp4
    echo.
    echo 🎯 Próximos passos:
    echo 1. Verificar o vídeo gerado
    echo 2. Ajustar configurações se necessário
    echo 3. Executar renderização em massa
    echo.
) else (
    echo.
    echo ❌ ERRO durante renderização
    echo.
)

echo 📊 Arquivos na pasta output:
dir /b "output\*.mp4" 2>nul || echo Nenhum arquivo MP4 encontrado

echo.
echo 🎉 Teste concluído!
pause
goto :end

:error
echo.
echo ❌ Erro na verificação inicial
pause

:end
