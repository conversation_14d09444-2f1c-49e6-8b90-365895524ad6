!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Motion={},t.React)}(this,function(t,e){"use strict";function i(t){const i=e.useRef(null);return null===i.current&&(i.current=t()),i.current}function n(t){let e;return()=>(void 0===e&&(e=t()),e)}const s=t=>t,a=t=>1e3*t,o=t=>t/1e3,r=t=>null!==t;class l{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}function u(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const h=t=>t.startsWith("--");const d=n(()=>void 0!==window.ScrollTimeline),m={};function c(t,e){const i=n(t);return()=>m[e]??i()}const p=c(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),f=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,y={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:f([0,.65,.55,1]),circOut:f([.55,0,1,.45]),backIn:f([.31,.01,.66,-.59]),backOut:f([.33,1.53,.69,.99])};function g(t,e){return t?"function"==typeof t?p()?((t,e,i=10)=>{let n="";const s=Math.max(Math.round(e/i),2);for(let e=0;e<s;e++)n+=Math.round(1e4*t(e/(s-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`})(t,e):"ease-out":(t=>Array.isArray(t)&&"number"==typeof t[0])(t)?f(t):Array.isArray(t)?t.map(t=>g(t,e)||y.easeOut):y[t]:void 0}class T extends l{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:i,keyframes:n,pseudoElement:s,allowFlatten:a=!1,finalKeyframe:o,onComplete:l}=t;this.isPseudoElement=Boolean(s),this.allowFlatten=a,this.options=t,t.type;const u=function({type:t,...e}){return function(t){return"function"==typeof t&&"applyToOptions"in t}(t)&&p()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:s=300,repeat:a=0,repeatType:o="loop",ease:r="easeOut",times:l}={},u){const h={[e]:i};l&&(h.offset=l);const d=g(r,s);Array.isArray(d)&&(h.easing=d);const m={delay:n,duration:s,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:a+1,direction:"reverse"===o?"alternate":"normal"};return u&&(m.pseudoElement=u),t.animate(h,m)}(e,i,n,u,s),!1===u.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){const t=function(t,{repeat:e,repeatType:i="loop"},n,s=1){const a=t.filter(r),o=s<0||e&&"loop"!==i&&e%2==1?0:a.length-1;return o&&void 0!==n?n:a[o]}(n,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){h(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}l?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return o(Number(t))}get time(){return o(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=a(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&d()?(this.animation.timeline=t,s):e(this)}}class b{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>t.finished))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t){const e=this.animations.map(e=>e.attachTimeline(t));return()=>{e.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class A extends b{then(t,e){return this.finished.finally(t).then(()=>{})}}const w=new WeakMap,E=(t,e="")=>`${t}:${e}`;function R(t){const e=w.get(t)||new Map;return w.set(t,e),e}function v(t,e){return t?.[e]??t?.default??t}const S=new Set(["borderWidth","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","borderRadius","radius","borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius","width","maxWidth","height","maxHeight","top","right","bottom","left","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","margin","marginTop","marginRight","marginBottom","marginLeft","backgroundPositionX","backgroundPositionY"]);function k(t,e){for(let i=0;i<t.length;i++)"number"==typeof t[i]&&S.has(e)&&(t[i]=t[i]+"px")}function M(t,e){const i=window.getComputedStyle(t);return h(e)?i.getPropertyValue(e):i[e]}function x(t,e,i,n){const s=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let n=document;e&&(n=e.current);const s=i?.[t]??n.querySelectorAll(t);return s?Array.from(s):[]}return Array.from(t)}(t,n),o=s.length,r=[];for(let t=0;t<o;t++){const n=s[t],l={...i};"function"==typeof l.delay&&(l.delay=l.delay(t,o));for(const t in e){let i=e[t];Array.isArray(i)||(i=[i]);const s={...v(l,t)};s.duration&&(s.duration=a(s.duration)),s.delay&&(s.delay=a(s.delay));const o=R(n),u=E(t,s.pseudoElement||""),h=o.get(u);h&&h.stop(),r.push({map:o,key:u,unresolvedKeyframes:i,options:{...s,element:n,name:t,allowFlatten:!l.type&&!l.ease}})}}for(let t=0;t<r.length;t++){const{unresolvedKeyframes:e,options:i}=r[t],{element:n,name:s,pseudoElement:a}=i;a||null!==e[0]||(e[0]=M(n,s)),u(e),k(e,s),!a&&e.length<2&&e.unshift(M(n,s)),i.keyframes=e}const l=[];for(let t=0;t<r.length;t++){const{map:e,key:i,options:n}=r[t],s=new T(n);e.set(i,s),s.finished.finally(()=>e.delete(i)),l.push(s)}return l}t.useAnimate=function(){const t=i(()=>({current:null,animations:[]})),n=i(()=>(t=>function(e,i,n){return new A(x(e,i,n,t))})(t));var s;return s=()=>{t.animations.forEach(t=>t.stop())},e.useEffect(()=>()=>s(),[]),[t,n]}});
