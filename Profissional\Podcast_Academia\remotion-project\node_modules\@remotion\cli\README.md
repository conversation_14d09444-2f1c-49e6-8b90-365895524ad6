# @remotion/cli
 
Control Remotion features using the `npx remotion` command
 
[![NPM Downloads](https://img.shields.io/npm/dm/@remotion/cli.svg?style=flat&color=black&label=Downloads)](https://npmcharts.com/compare/@remotion/cli?minimal=true)
 
## Installation
 
```bash
npm install @remotion/cli --save-exact
```
 
When installing a Remotion package, make sure to align the version of all `remotion` and `@remotion/*` packages to the same version.
Remove the `^` character from the version number to use the exact version.
 
## Usage
 
See the [documentation](https://www.remotion.dev/docs/cli) for more information.
