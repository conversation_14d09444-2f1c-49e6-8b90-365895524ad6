import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { Animated, Move, Fade, Scale } from 'remotion-animated';
import { noise2D } from '@remotion/noise';

interface AnimatedTitleProps {
  title: string;
  subtitle?: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
  };
  startFrame?: number;
  endFrame?: number;
}

export const AnimatedTitle: React.FC<AnimatedTitleProps> = ({
  title,
  subtitle,
  colors,
  startFrame = 0,
  endFrame = 120,
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // Animações cinematográficas avançadas
  const titleOpacity = interpolate(
    frame,
    [startFrame, startFrame + 30, endFrame - 30, endFrame],
    [0, 1, 1, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  const titleScale = interpolate(
    frame,
    [startFrame, startFrame + 20, startFrame + 40],
    [0.8, 1.1, 1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  // Efeito de ruído para movimento orgânico
  const noiseX = noise2D('titleX', frame * 0.01, 0) * 2;
  const noiseY = noise2D('titleY', 0, frame * 0.01) * 1;

  // Gradiente animado
  const gradientOffset = interpolate(
    frame,
    [0, fps * 2],
    [0, 100],
    { extrapolateRight: 'mirror' }
  );

  return (
    <div
      style={{
        position: 'absolute',
        top: '20%',
        left: '50%',
        transform: `translate(-50%, -50%) translate(${noiseX}px, ${noiseY}px)`,
        textAlign: 'center',
        zIndex: 10,
      }}
    >
      {/* Título Principal com Animações Avançadas */}
      <Animated
        animations={[
          Fade({ to: titleOpacity, initial: 0 }),
          Scale({ by: titleScale, initial: 0.8 }),
          Move({ y: -20, start: startFrame + 10 }),
        ]}
      >
        <h1
          style={{
            fontSize: '4rem',
            fontWeight: 'bold',
            margin: 0,
            padding: '20px',
            background: `linear-gradient(45deg, ${colors.primary}, ${colors.accent})`,
            backgroundSize: '200% 200%',
            backgroundPosition: `${gradientOffset}% 50%`,
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            textShadow: '0 4px 20px rgba(0,0,0,0.3)',
            filter: 'drop-shadow(0 2px 10px rgba(0,0,0,0.5))',
          }}
        >
          {title}
        </h1>
      </Animated>

      {/* Subtitle com Delay */}
      {subtitle && (
        <Animated
          animations={[
            Fade({ to: 1, initial: 0, start: startFrame + 20 }),
            Move({ y: 20, start: startFrame + 25 }),
          ]}
        >
          <h2
            style={{
              fontSize: '1.8rem',
              fontWeight: '300',
              margin: '10px 0',
              color: colors.secondary,
              textShadow: '0 2px 10px rgba(0,0,0,0.5)',
              opacity: interpolate(
                frame,
                [startFrame + 20, startFrame + 50, endFrame - 20, endFrame],
                [0, 1, 1, 0],
                { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
              ),
            }}
          >
            {subtitle}
          </h2>
        </Animated>
      )}

      {/* Elemento decorativo animado */}
      <Animated
        animations={[
          Scale({ by: 1, initial: 0, start: startFrame + 30 }),
          Fade({ to: 0.8, initial: 0, start: startFrame + 30 }),
        ]}
      >
        <div
          style={{
            width: '100px',
            height: '4px',
            background: `linear-gradient(90deg, ${colors.accent}, transparent)`,
            margin: '20px auto',
            borderRadius: '2px',
            transform: `scaleX(${interpolate(
              frame,
              [startFrame + 30, startFrame + 60],
              [0, 1],
              { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
            )})`,
          }}
        />
      </Animated>
    </div>
  );
};
