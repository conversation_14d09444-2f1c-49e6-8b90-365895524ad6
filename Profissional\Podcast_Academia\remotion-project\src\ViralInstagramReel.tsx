import React from "react";
import {
  AbsoluteFill,
  Video,
  Audio,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  staticFile,
  Sequence,
  spring,
} from "remotion";

interface ViralInstagramReelProps {
  videoPath: string;
  audioPath: string;
  startTime: number;
  duration: number;
  title: string;
  subtitle: string;
  hook: string;
  cta: string;
  author: string;
}

export const ViralInstagramReel: React.FC<ViralInstagramReelProps> = ({
  videoPath,
  audioPath,
  startTime,
  duration,
  title,
  subtitle,
  hook,
  cta,
  author,
}) => {
  const frame = useCurrentFrame();
  const { durationInFrames, fps } = useVideoConfig();

  // Configurações de timing viral
  const hookDuration = 3 * fps; // 3 segundos para hook
  const mainContentStart = hookDuration;
  const ctaDuration = 2 * fps; // 2 segundos para CTA
  const ctaStart = durationInFrames - ctaDuration;

  // Animações baseadas em padrões virais
  const hookScale = spring({
    frame,
    fps,
    config: {
      damping: 200,
      stiffness: 100,
    },
  });

  const hookOpacity = interpolate(
    frame,
    [0, 15, hookDuration - 15, hookDuration],
    [0, 1, 1, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  const mainContentOpacity = interpolate(
    frame,
    [mainContentStart, mainContentStart + 15, ctaStart - 15, ctaStart],
    [0, 1, 1, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  const ctaScale = spring({
    frame: frame - ctaStart,
    fps,
    config: {
      damping: 100,
      stiffness: 200,
    },
  });

  const ctaOpacity = interpolate(
    frame,
    [ctaStart, ctaStart + 15, durationInFrames - 5, durationInFrames],
    [0, 1, 1, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  // Progress bar viral
  const progress = interpolate(
    frame,
    [0, durationInFrames],
    [0, 100],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  return (
    <AbsoluteFill>
      {/* Áudio sincronizado - segmento já extraído */}
      <Audio
        src={staticFile(audioPath)}
        startFrom={0}
        endAt={duration * fps}
        volume={0.9}
      />

      {/* Vídeo principal com crop para 9:16 */}
      <div
        style={{
          width: "100%",
          height: "100%",
          background: "#000",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Video
          src={staticFile(videoPath)}
          startFrom={startTime * fps}
          endAt={(startTime + duration) * fps}
          style={{
            width: "100%",
            height: "133.33%", // Crop 16:9 para 9:16
            objectFit: "cover",
            objectPosition: "center",
          }}
          muted={true}
        />
      </div>

      {/* Hook viral - primeiros 3 segundos */}
      {frame < hookDuration && (
        <AbsoluteFill
          style={{
            background: "linear-gradient(45deg, rgba(0,0,0,0.7), rgba(0,0,0,0.3))",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            opacity: hookOpacity,
          }}
        >
          <div
            style={{
              transform: `scale(${hookScale})`,
              textAlign: "center",
              padding: "40px",
            }}
          >
            <h1
              style={{
                fontSize: "3.5rem",
                fontWeight: "900",
                color: "#fff",
                margin: "0 0 20px 0",
                fontFamily: "'Inter', -apple-system, sans-serif",
                textShadow: "0 4px 20px rgba(0,0,0,0.8)",
                lineHeight: "1.1",
                letterSpacing: "-0.02em",
              }}
            >
              {hook}
            </h1>
            <div
              style={{
                width: "80px",
                height: "4px",
                background: "linear-gradient(90deg, #ff6b35, #f7931e)",
                margin: "0 auto",
                borderRadius: "2px",
              }}
            />
          </div>
        </AbsoluteFill>
      )}

      {/* Conteúdo principal */}
      {frame >= mainContentStart && frame < ctaStart && (
        <>
          {/* Título e subtítulo */}
          <div
            style={{
              position: "absolute",
              top: "60px",
              left: "30px",
              right: "30px",
              opacity: mainContentOpacity,
            }}
          >
            <h2
              style={{
                fontSize: "1.8rem",
                fontWeight: "700",
                color: "#fff",
                margin: "0 0 10px 0",
                fontFamily: "'Inter', -apple-system, sans-serif",
                textShadow: "0 2px 10px rgba(0,0,0,0.8)",
                lineHeight: "1.2",
              }}
            >
              {title}
            </h2>
            <p
              style={{
                fontSize: "1.1rem",
                fontWeight: "400",
                color: "#f0f0f0",
                margin: "0",
                fontFamily: "'Inter', -apple-system, sans-serif",
                textShadow: "0 1px 5px rgba(0,0,0,0.6)",
                opacity: 0.9,
              }}
            >
              {subtitle}
            </p>
          </div>

          {/* Branding sutil */}
          <div
            style={{
              position: "absolute",
              top: "30px",
              right: "30px",
              padding: "8px 16px",
              background: "rgba(0,0,0,0.6)",
              borderRadius: "20px",
              border: "1px solid rgba(255,255,255,0.2)",
              opacity: mainContentOpacity,
            }}
          >
            <span
              style={{
                fontSize: "0.9rem",
                fontWeight: "600",
                color: "#fff",
                fontFamily: "'Inter', -apple-system, sans-serif",
              }}
            >
              @{author}
            </span>
          </div>
        </>
      )}

      {/* CTA viral - últimos 2 segundos */}
      {frame >= ctaStart && (
        <AbsoluteFill
          style={{
            background: "linear-gradient(135deg, rgba(255,107,53,0.9), rgba(247,147,30,0.9))",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            opacity: ctaOpacity,
          }}
        >
          <div
            style={{
              transform: `scale(${ctaScale})`,
              textAlign: "center",
              padding: "40px",
            }}
          >
            <h1
              style={{
                fontSize: "2.8rem",
                fontWeight: "900",
                color: "#fff",
                margin: "0 0 20px 0",
                fontFamily: "'Inter', -apple-system, sans-serif",
                textShadow: "0 4px 20px rgba(0,0,0,0.3)",
                lineHeight: "1.1",
              }}
            >
              {cta}
            </h1>
            <div
              style={{
                padding: "15px 30px",
                background: "#fff",
                borderRadius: "30px",
                display: "inline-block",
                boxShadow: "0 8px 25px rgba(0,0,0,0.3)",
              }}
            >
              <span
                style={{
                  fontSize: "1.2rem",
                  fontWeight: "700",
                  color: "#ff6b35",
                  fontFamily: "'Inter', -apple-system, sans-serif",
                }}
              >
                Saiba mais →
              </span>
            </div>
          </div>
        </AbsoluteFill>
      )}

      {/* Progress bar minimalista */}
      <div
        style={{
          position: "absolute",
          bottom: "20px",
          left: "30px",
          right: "30px",
          height: "3px",
          background: "rgba(255,255,255,0.3)",
          borderRadius: "2px",
          overflow: "hidden",
        }}
      >
        <div
          style={{
            width: `${progress}%`,
            height: "100%",
            background: "linear-gradient(90deg, #ff6b35, #f7931e)",
            borderRadius: "2px",
            transition: "width 0.1s ease-out",
          }}
        />
      </div>

      {/* Elementos de engajamento sutis */}
      <div
        style={{
          position: "absolute",
          bottom: "80px",
          right: "30px",
          display: "flex",
          flexDirection: "column",
          gap: "15px",
          opacity: mainContentOpacity,
        }}
      >
        {/* Like */}
        <div
          style={{
            width: "50px",
            height: "50px",
            background: "rgba(255,255,255,0.1)",
            borderRadius: "25px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            border: "1px solid rgba(255,255,255,0.2)",
          }}
        >
          <span style={{ fontSize: "1.5rem" }}>❤️</span>
        </div>
        
        {/* Comment */}
        <div
          style={{
            width: "50px",
            height: "50px",
            background: "rgba(255,255,255,0.1)",
            borderRadius: "25px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            border: "1px solid rgba(255,255,255,0.2)",
          }}
        >
          <span style={{ fontSize: "1.5rem" }}>💬</span>
        </div>
        
        {/* Share */}
        <div
          style={{
            width: "50px",
            height: "50px",
            background: "rgba(255,255,255,0.1)",
            borderRadius: "25px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            border: "1px solid rgba(255,255,255,0.2)",
          }}
        >
          <span style={{ fontSize: "1.5rem" }}>📤</span>
        </div>
      </div>
    </AbsoluteFill>
  );
};
