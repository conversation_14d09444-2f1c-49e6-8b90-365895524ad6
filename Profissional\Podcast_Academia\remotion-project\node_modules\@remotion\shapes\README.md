# @remotion/shapes
 
Generate SVG shapes
 
[![NPM Downloads](https://img.shields.io/npm/dm/@remotion/shapes.svg?style=flat&color=black&label=Downloads)](https://npmcharts.com/compare/@remotion/shapes?minimal=true)
 
## Installation
 
```bash
npm install @remotion/shapes --save-exact
```
 
When installing a Remotion package, make sure to align the version of all `remotion` and `@remotion/*` packages to the same version.
Remove the `^` character from the version number to use the exact version.
 
## Usage
 
See the [documentation](https://www.remotion.dev/docs/shapes) for more information.
