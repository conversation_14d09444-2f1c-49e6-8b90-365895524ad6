import React from "react";
import {
  AbsoluteFill,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
} from "remotion";

interface BrandingOverlayProps {
  frame: number;
  author: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
  position: "top-left" | "top-right" | "bottom-left" | "bottom-right";
}

export const BrandingOverlay: React.FC<BrandingOverlayProps> = ({
  frame,
  author,
  colors,
  position
}) => {
  const { fps } = useVideoConfig();
  
  // Animação de pulsação sutil
  const pulse = interpolate(
    frame % (fps * 3),
    [0, fps * 1.5, fps * 3],
    [1, 1.05, 1],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );
  
  // Posicionamento baseado na prop position
  const getPositionStyles = () => {
    switch (position) {
      case "top-left":
        return { top: 40, left: 40 };
      case "top-right":
        return { top: 40, right: 40 };
      case "bottom-left":
        return { bottom: 100, left: 40 };
      case "bottom-right":
        return { bottom: 100, right: 40 };
      default:
        return { bottom: 100, right: 40 };
    }
  };
  
  const positionStyles = getPositionStyles();
  
  return (
    <AbsoluteFill>
      {/* Logo/Brand container */}
      <div
        style={{
          position: "absolute",
          ...positionStyles,
          transform: `scale(${pulse})`,
          zIndex: 1000,
        }}
      >
        <div
          style={{
            background: `linear-gradient(135deg, ${colors.primary}E6, ${colors.secondary}E6)`,
            borderRadius: 25,
            padding: "12px 20px",
            display: "flex",
            alignItems: "center",
            gap: 12,
            boxShadow: "0 8px 25px rgba(0,0,0,0.3)",
            border: `2px solid ${colors.accent}`,
            backdropFilter: "blur(15px)",
          }}
        >
          {/* Avatar/Logo */}
          <div
            style={{
              width: 40,
              height: 40,
              borderRadius: "50%",
              background: `linear-gradient(135deg, ${colors.accent}, ${colors.primary})`,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              fontSize: 18,
              fontWeight: "bold",
              color: colors.text,
              textShadow: "1px 1px 2px rgba(0,0,0,0.5)",
            }}
          >
            {author.charAt(0).toUpperCase()}
          </div>
          
          {/* Author name */}
          <div
            style={{
              color: colors.text,
              fontSize: 18,
              fontWeight: "bold",
              textShadow: "1px 1px 2px rgba(0,0,0,0.5)",
            }}
          >
            @{author}
          </div>
          
          {/* Verified badge */}
          <div
            style={{
              fontSize: 16,
              filter: "drop-shadow(1px 1px 2px rgba(0,0,0,0.5))",
            }}
          >
            ✅
          </div>
        </div>
      </div>
      
      {/* Watermark sutil */}
      <div
        style={{
          position: "absolute",
          bottom: 40,
          left: 40,
          opacity: 0.6,
          fontSize: 14,
          color: colors.text,
          textShadow: "1px 1px 2px rgba(0,0,0,0.7)",
          fontWeight: "500",
        }}
      >
        🤖 Criado com IA
      </div>
      
      {/* Hashtags trending */}
      <div
        style={{
          position: "absolute",
          top: 120,
          left: 40,
          display: "flex",
          flexWrap: "wrap",
          gap: 8,
          maxWidth: 300,
        }}
      >
        {["#IA", "#AgentesIA", "#Tech", "#Inovação"].map((hashtag, index) => (
          <div
            key={hashtag}
            style={{
              background: `${colors.accent}40`,
              color: colors.text,
              padding: "4px 12px",
              borderRadius: 15,
              fontSize: 14,
              fontWeight: "600",
              textShadow: "1px 1px 2px rgba(0,0,0,0.5)",
              border: `1px solid ${colors.accent}60`,
              backdropFilter: "blur(5px)",
              opacity: interpolate(
                frame,
                [index * 30, index * 30 + 30],
                [0, 1],
                { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
              ),
            }}
          >
            {hashtag}
          </div>
        ))}
      </div>
      
      {/* Quality indicator */}
      <div
        style={{
          position: "absolute",
          top: 50,
          left: 40,
          background: `linear-gradient(135deg, ${colors.primary}CC, ${colors.accent}CC)`,
          padding: "6px 12px",
          borderRadius: 15,
          border: `1px solid ${colors.text}`,
          backdropFilter: "blur(10px)",
          boxShadow: "0 4px 15px rgba(0,0,0,0.2)",
        }}
      >
        <div
          style={{
            color: colors.text,
            fontSize: 12,
            fontWeight: "bold",
            textShadow: "1px 1px 2px rgba(0,0,0,0.5)",
          }}
        >
          ⭐ CONTEÚDO PREMIUM
        </div>
      </div>
    </AbsoluteFill>
  );
};
