"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useCurrentGifIndex = useCurrentGifIndex;
const react_1 = require("react");
const remotion_1 = require("remotion");
function useCurrentGifIndex({ delays, loopBehavior, playbackRate, }) {
    const currentFrame = (0, remotion_1.useCurrentFrame)();
    const videoConfig = (0, remotion_1.useVideoConfig)();
    const duration = (0, react_1.useMemo)(() => {
        if (delays.length !== 0) {
            return delays.reduce((sum, delay) => sum + (delay !== null && delay !== void 0 ? delay : 0), 0);
        }
        return 1;
    }, [delays]);
    if (delays.length === 0) {
        return 0;
    }
    const updatedFrame = currentFrame / (1 / playbackRate);
    const time = (updatedFrame / videoConfig.fps) * 1000;
    if (loopBehavior === 'pause-after-finish' && time >= duration) {
        return delays.length - 1;
    }
    if (loopBehavior === 'unmount-after-finish' && time >= duration) {
        return -1;
    }
    let currentTime = time % duration;
    for (let i = 0; i < delays.length; i++) {
        const delay = delays[i];
        if (currentTime < delay) {
            return i;
        }
        currentTime -= delay;
    }
    return 0;
}
