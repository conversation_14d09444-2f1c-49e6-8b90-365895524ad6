"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.inlineCodeSnippet = exports.outerPortal = exports.fullScreenOverlay = exports.menuContainerTowardsTop = exports.menuContainerTowardsBottom = exports.SHADOW_TOWARDS_TOP = exports.SHADOW_TOWARDS_BOTTOM = exports.MAX_MOBILE_MENU_WIDTH = exports.MAX_MENU_WIDTH = exports.SUBMENU_LEFT_INSET = exports.MENU_VERTICAL_PADDING = void 0;
const colors_1 = require("../../helpers/colors");
exports.MENU_VERTICAL_PADDING = 4;
exports.SUBMENU_LEFT_INSET = -8;
exports.MAX_MENU_WIDTH = 400;
exports.MAX_MOBILE_MENU_WIDTH = 300;
const menuContainer = {
    backgroundColor: colors_1.BACKGROUND,
    position: 'fixed',
    color: 'white',
    userSelect: 'none',
    WebkitUserSelect: 'none',
};
exports.SHADOW_TOWARDS_BOTTOM = '0 2px 8px rgba(0, 0, 0, 0.5)';
exports.SHADOW_TOWARDS_TOP = '0 -2px 8px rgba(0, 0, 0, 0.5)';
exports.menuContainerTowardsBottom = {
    ...menuContainer,
    boxShadow: exports.SHADOW_TOWARDS_BOTTOM,
};
exports.menuContainerTowardsTop = {
    ...menuContainer,
    boxShadow: exports.SHADOW_TOWARDS_TOP,
};
exports.fullScreenOverlay = {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
};
exports.outerPortal = {
    position: 'fixed',
};
exports.inlineCodeSnippet = {
    fontSize: 14,
    color: colors_1.BLUE,
    fontFamily: 'monospace',
};
