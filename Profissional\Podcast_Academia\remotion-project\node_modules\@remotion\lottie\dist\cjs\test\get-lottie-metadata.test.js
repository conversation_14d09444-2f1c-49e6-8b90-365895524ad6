"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const bun_test_1 = require("bun:test");
const node_fs_1 = __importDefault(require("node:fs"));
const node_path_1 = __importDefault(require("node:path"));
const get_lottie_metadata_1 = require("../get-lottie-metadata");
(0, bun_test_1.test)('Should be able to get Lottie metadata', () => {
    const file = node_fs_1.default.readFileSync(node_path_1.default.join(__dirname, 'example.json'), 'utf-8');
    const parsed = JSON.parse(file);
    (0, bun_test_1.expect)((0, get_lottie_metadata_1.getLottieMetadata)(parsed)).toEqual({
        durationInFrames: 90,
        durationInSeconds: 3.0030030030030037,
        fps: 29.9700012207031,
        height: 1080,
        width: 1920,
    });
});
(0, bun_test_1.test)('Should return null if invalid Lottie file', () => {
    // @ts-expect-error
    (0, bun_test_1.expect)((0, get_lottie_metadata_1.getLottieMetadata)({})).toEqual(null);
});
