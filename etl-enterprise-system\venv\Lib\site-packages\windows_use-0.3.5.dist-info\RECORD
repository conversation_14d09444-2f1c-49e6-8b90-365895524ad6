windows_use-0.3.5.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
windows_use-0.3.5.dist-info/METADATA,sha256=WvtcfQWSBjhTKqPnwrs1Jc6CDQdW21FMvssPq-jSNuQ,6828
windows_use-0.3.5.dist-info/RECORD,,
windows_use-0.3.5.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
windows_use-0.3.5.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
windows_use-0.3.5.dist-info/licenses/LICENSE,sha256=AKhk5IIis1Oikc6A8_2vMlLo5fiplHfnZzEan1cyWPU,1087
windows_use/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
windows_use/__pycache__/__init__.cpython-313.pyc,,
windows_use/agent/__init__.py,sha256=PmqF7OZ_IaDopPB5RYCXVAld0Km58No2py8NLwiIMnE,72
windows_use/agent/__pycache__/__init__.cpython-313.pyc,,
windows_use/agent/__pycache__/service.cpython-313.pyc,,
windows_use/agent/__pycache__/utils.cpython-313.pyc,,
windows_use/agent/__pycache__/views.cpython-313.pyc,,
windows_use/agent/prompt/__pycache__/service.cpython-313.pyc,,
windows_use/agent/prompt/action.md,sha256=1uskFwmgysmWRvDTQpBZ1ZMWSZSI7cl0xXJbiU2Q5bg,229
windows_use/agent/prompt/answer.md,sha256=WJG8HfCTK2Rli889fOPsDTECylHPTaeniH9I35Gx-yc,183
windows_use/agent/prompt/observation.md,sha256=ALKZgcTd_Haheybhw9fWmnRv2RwthPYouVPaPOFTHxY,804
windows_use/agent/prompt/service.py,sha256=4oCttXztfr_bSRAYwhHruRVBmncdLS1UX-fhF8SHjM8,3569
windows_use/agent/prompt/system.md,sha256=X1TxmgpRyfs9JiHG732ww1oZgwctK-mtlxfLkUbdKu4,9956
windows_use/agent/registry/__pycache__/service.cpython-313.pyc,,
windows_use/agent/registry/__pycache__/views.cpython-313.pyc,,
windows_use/agent/registry/service.py,sha256=I9qxN7HaC6qOLUXGUZ2CH2vP9M7HQv0rS02K4IlYQMo,1573
windows_use/agent/registry/views.py,sha256=b4awGXjbVllm9vg6LTxaVmemJe6RasM_m1AY31hDHQM,278
windows_use/agent/service.py,sha256=hoskVoU450q_R0CKtDBqP1WDGm8qhv0biBiuCIAMMZo,8311
windows_use/agent/tools/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
windows_use/agent/tools/__pycache__/__init__.cpython-313.pyc,,
windows_use/agent/tools/__pycache__/service.cpython-313.pyc,,
windows_use/agent/tools/__pycache__/views.cpython-313.pyc,,
windows_use/agent/tools/service.py,sha256=vFM6zPoq4cpqT9aCRYDSWvSd66OOO08p_cBvpOgaS1c,7886
windows_use/agent/tools/views.py,sha256=E_VgfCMa79xA0VJODqHSapIf6Hc6w7Yrdp9opHtJcb8,3497
windows_use/agent/utils.py,sha256=6X7EAD4l9xTumE5PmkwW6W0tM3u7-HqvAotfMEwXYN4,2072
windows_use/agent/views.py,sha256=-4sGcw8w5l-NPyYOea8_KiJ0gi0QL702z9kOCiT9uWM,1660
windows_use/desktop/__init__.py,sha256=dK1Mvfk1lU1yj5nqRNVyQxBNgPqnSw9m_iOJTmoJ46U,6195
windows_use/desktop/__pycache__/__init__.cpython-313.pyc,,
windows_use/desktop/__pycache__/config.cpython-313.pyc,,
windows_use/desktop/__pycache__/views.cpython-313.pyc,,
windows_use/desktop/config.py,sha256=CidB3b5P6OZfzJMuhT2W485IgEn3yI5jr4zj3vG5AZo,171
windows_use/desktop/views.py,sha256=6XK5Cm1g74M8br_O0D4oOHpk7GNcqXqBcTPy6izqV3U,1015
windows_use/tree/__init__.py,sha256=4VRrX9NutjAGjBjW8MlhCYdU4QWXGsmGpyAvEkLEgTA,13240
windows_use/tree/__pycache__/__init__.cpython-313.pyc,,
windows_use/tree/__pycache__/config.cpython-313.pyc,,
windows_use/tree/__pycache__/utils.cpython-313.pyc,,
windows_use/tree/__pycache__/views.cpython-313.pyc,,
windows_use/tree/config.py,sha256=F-3keLk-UlI7OsG607mZ2awp0aq5nRTh-3YzPGNWH34,560
windows_use/tree/utils.py,sha256=6hbxdIQPrAY-I3jcHsRqodHlxboTQj2GnLA71bf1lqY,911
windows_use/tree/views.py,sha256=HPdq8rNwnxbeM6vB9v33cx-48JRWWzRWRogFQRhAcmA,2229
