"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getGifDurationInSeconds = void 0;
const remotion_1 = require("remotion");
const gif_cache_1 = require("./gif-cache");
const react_tools_1 = require("./react-tools");
const resolve_gif_source_1 = require("./resolve-gif-source");
const calcDuration = (parsed) => {
    return (parsed.delays.reduce((sum, delay) => sum + delay, 0) / 1000);
};
/*
 * @description Gets the duration in seconds of a GIF.
 * @see [Documentation](https://remotion.dev/docs/gif/get-gif-duration-in-seconds)
 */
const getGifDurationInSeconds = async (src) => {
    var _a;
    const resolvedSrc = (0, resolve_gif_source_1.resolveGifSource)(src);
    const inCache = (_a = gif_cache_1.volatileGifCache.get(resolvedSrc)) !== null && _a !== void 0 ? _a : gif_cache_1.manuallyManagedGifCache.get(resolvedSrc);
    if (inCache) {
        return calcDuration(inCache);
    }
    if ((0, remotion_1.getRemotionEnvironment)().isRendering) {
        const renderingParsed = (0, react_tools_1.parseWithWorker)(resolvedSrc);
        const resolved = await renderingParsed.prom;
        gif_cache_1.volatileGifCache.set(resolvedSrc, resolved);
        return calcDuration(resolved);
    }
    const parsed = await (0, react_tools_1.parseGif)({
        src: resolvedSrc,
        controller: new AbortController(),
    });
    gif_cache_1.volatileGifCache.set(resolvedSrc, parsed);
    return calcDuration(parsed);
};
exports.getGifDurationInSeconds = getGifDurationInSeconds;
