type Style = {
    codes: [number, number];
    name: string;
    wrap?: (input: string, newline: boolean) => string;
};
type Method = (str: string) => string;
type Colors = {
    enabled: () => boolean;
    visible: boolean;
    styles: Record<string, Style>;
    keys: Record<string, string[]>;
    alias?: (name: string, col: string) => void;
};
type ColorsWithMethods = Colors & {
    reset: Method;
    bold: Method;
    dim: Method;
    italic: Method;
    underline: Method;
    inverse: Method;
    hidden: Method;
    strikethrough: Method;
    black: Method;
    red: Method;
    green: Method;
    yellow: Method;
    blue: Method;
    magenta: Method;
    cyan: Method;
    white: Method;
    gray: Method;
    bgBlack: Method;
    bgRed: Method;
    bgGreen: Method;
    bgYellow: Method;
    bgBlue: Method;
    bgMagenta: Method;
    bgWhite: Method;
    blackBright: Method;
    redBright: Method;
    greenBright: Method;
    yellowBright: Method;
    blueBright: Method;
    magentaBright: Method;
    whiteBright: Method;
    bgBlackBright: Method;
    bgRedBright: Method;
    bgGreenBright: Method;
    bgYellowBright: Method;
    bgBlueBright: Method;
    bgMagentaBright: Method;
    bgWhiteBright: Method;
};
export declare const chalk: ColorsWithMethods;
export {};
