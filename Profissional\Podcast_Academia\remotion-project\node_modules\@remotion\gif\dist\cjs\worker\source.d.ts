export declare const src = "\"use strict\";(()=>{var P=(t,r,e={},n=e)=>{if(Array.isArray(r))r.forEach(o=>P(t,o,e,n));else if(typeof r==\"function\")r(t,e,n,P);else{let o=Object.keys(r)[0];Array.isArray(r[o])?(n[o]={},P(t,r[o],e,n[o])):n[o]=r[o](t,e,n,P)}return e},M=(t,r)=>function(e,n,o,c){let i=[],a=e.pos;for(;r(e,n,o);){let s={};if(c(e,t,n,s),e.pos===a)break;a=e.pos,i.push(s)}return i},g=(t,r)=>(e,n,o,c)=>{r(e,n,o)&&c(e,t,n,o)};var W=t=>({data:t,pos:0}),m=()=>t=>t.data[t.pos++],U=(t=0)=>r=>r.data[r.pos+t],f=t=>r=>r.data.subarray(r.pos,r.pos+=t),k=t=>r=>r.data.subarray(r.pos,r.pos+t),v=t=>r=>Array.from(f(t)(r)).map(e=>String.fromCharCode(e)).join(\"\"),b=t=>r=>{let e=f(2)(r);return t?(e[1]<<8)+e[0]:(e[0]<<8)+e[1]},E=(t,r)=>(e,n,o)=>{let c=typeof r==\"function\"?r(e,n,o):r,i=f(t),a=new Array(c);for(let s=0;s<c;s++)a[s]=i(e);return a},$=(t,r,e)=>{let n=0;for(let o=0;o<e;o++)n+=Number(t[r+o]&&2**(e-o-1));return n},I=t=>r=>{let e=m()(r),n=new Array(8);for(let o=0;o<8;o++)n[7-o]=!!(e&1<<o);return Object.keys(t).reduce((o,c)=>{let i=t[c];return i.length?o[c]=$(n,i.index,i.length):o[c]=n[i.index],o},{})};var z={blocks:t=>{let e=[],n=t.data.length,o=0;for(let a=m()(t);a!==0&&a;a=m()(t)){if(t.pos+a>=n){let s=n-t.pos;e.push(f(s)(t)),o+=s;break}e.push(f(a)(t)),o+=a}let c=new Uint8Array(o),i=0;for(let a=0;a<e.length;a++)c.set(e[a],i),i+=e[a].length;return c}},q=g({gce:[{codes:f(2)},{byteSize:m()},{extras:I({future:{index:0,length:3},disposal:{index:3,length:3},userInput:{index:6},transparentColorGiven:{index:7}})},{delay:b(!0)},{transparentColorIndex:m()},{terminator:m()}]},t=>{let r=k(2)(t);return r[0]===33&&r[1]===249}),H=g({image:[{code:m()},{descriptor:[{left:b(!0)},{top:b(!0)},{width:b(!0)},{height:b(!0)},{lct:I({exists:{index:0},interlaced:{index:1},sort:{index:2},future:{index:3,length:2},size:{index:5,length:3}})}]},g({lct:E(3,(t,r,e)=>2**(e.descriptor.lct.size+1))},(t,r,e)=>e.descriptor.lct.exists),{data:[{minCodeSize:m()},z]}]},t=>U()(t)===44),J=g({text:[{codes:f(2)},{blockSize:m()},{preData:(t,r,e)=>f(e.text.blockSize)(t)},z]},t=>{let r=k(2)(t);return r[0]===33&&r[1]===1}),Q=g({application:[{codes:f(2)},{blockSize:m()},{id:(t,r,e)=>v(e.blockSize)(t)},z]},t=>{let r=k(2)(t);return r[0]===33&&r[1]===255}),V=g({comment:[{codes:f(2)},z]},t=>{let r=k(2)(t);return r[0]===33&&r[1]===254}),K=[{header:[{signature:v(3)},{version:v(3)}]},{lsd:[{width:b(!0)},{height:b(!0)},{gct:I({exists:{index:0},resolution:{index:1,length:3},sort:{index:4},size:{index:5,length:3}})},{backgroundColorIndex:m()},{pixelAspectRatio:m()}]},g({gct:E(3,(t,r)=>2**(r.lsd.gct.size+1))},(t,r)=>r.lsd.gct.exists),{frames:M([q,Q,V,H,J],t=>{let r=U()(t);return r===33||r===44})}];var X=(t,r)=>{let e=new Array(t.length),n=t.length/r,o=function(s,d){let u=t.slice(d*r,(d+1)*r);e.splice(...[s*r,r].concat(u))},c=[0,4,2,1],i=[8,8,4,2],a=0;for(let s=0;s<4;s++)for(let d=c[s];d<n;d+=i[s])o(d,a),a++;return e};var Z=(t,r,e)=>{let c=e,i,a,s,d,u;var w;let l,p;var C,y,h,_,G;let x=new Array(e),B=new Array(4096),T=new Array(4096),F=new Array(4097),R=t,S=1<<R,O=S+1;for(i=S+2,u=-1,s=R+1,a=(1<<s)-1,l=0;l<S;l++)B[l]=0,T[l]=l;var C,w,y,h,G,_;for(C=w=y=h=G=_=0,p=0;p<c;){if(h===0){if(w<s){C+=r[_]<<w,w+=8,_++;continue}if(l=C&a,C>>=s,w-=s,l>i||l===O)break;if(l===S){s=R+1,a=(1<<s)-1,i=S+2,u=-1;continue}if(u===-1){F[h++]=T[l],u=l,y=l;continue}for(d=l,l===i&&(F[h++]=y,l=u);l>S;)F[h++]=T[l],l=B[l];y=T[l]&255,F[h++]=y,i<4096&&(B[i]=u,T[i]=y,i++,(i&a)===0&&i<4096&&(s++,a+=i)),u=d}h--,x[G++]=F[h],p++}for(p=G;p<c;p++)x[p]=0;return x};var j=t=>{let r=new Uint8Array(t);return P(W(r),K)},D=(t,r)=>{var i,a,s;if(!t.image)return console.warn(\"gif frame does not have associated image.\"),null;let{image:e}=t,n=e.descriptor.width*e.descriptor.height,o=Z(e.data.minCodeSize,e.data.blocks,n);return(i=e.descriptor.lct)!=null&&i.interlaced&&(o=X(o,e.descriptor.width)),{pixels:o,dims:{top:t.image.descriptor.top,left:t.image.descriptor.left,width:t.image.descriptor.width,height:t.image.descriptor.height},colorTable:(a=e.descriptor.lct)!=null&&a.exists?e.lct:r,delay:(((s=t.gce)==null?void 0:s.delay)||10)*10,disposalType:t.gce?t.gce.extras.disposal:1,transparentIndex:t.gce&&t.gce.extras.transparentColorGiven?t.gce.transparentColorIndex:-1}};var L=t=>t.frames.filter(r=>\"image\"in r).map(r=>D(r,t.gct)).filter(Boolean).map(r=>r);var Y=t=>{let r=null;for(let e of t.frames)r=e.gce?e.gce:r,\"image\"in e&&!(\"gce\"in e)&&r!==null&&(e.gce=r)},ee=({typedArray:t,dx:r,dy:e,width:n,height:o,gifWidth:c})=>{let i=e*c+r;for(let a=0;a<o;a++)for(let s=0;s<n;s++){let d=i+a*c+s;t[d*4]=0,t[d*4+1]=0,t[d*4+2]=0,t[d*4+3]=0}},te=(t,r,e)=>{let{width:n,height:o,top:c,left:i}=r.dims,a=c*e.width+i;for(let s=0;s<o;s++)for(let d=0;d<n;d++){let u=s*n+d,l=r.pixels[u];if(l!==r.transparentIndex){let p=a+s*e.width+d,x=r.colorTable[l];t[p*4]=x[0],t[p*4+1]=x[1],t[p*4+2]=x[2],t[p*4+3]=l===r.transparentIndex?0:255}}},N=(t,{signal:r})=>fetch(t,{signal:r}).then(e=>{var n;if(!((n=e.headers.get(\"Content-Type\"))!=null&&n.includes(\"image/gif\")))throw Error(`Wrong content type: \"${e.headers.get(\"Content-Type\")}\"`);return e.arrayBuffer()}).then(e=>j(e)).then(e=>(Y(e),e)).then(e=>Promise.all([L(e),{width:e.lsd.width,height:e.lsd.height}])).then(([e,n])=>{let o=[],c=n.width*n.height*4,i=new Uint8ClampedArray(c);for(let a=0;a<e.length;++a){let s=e[a],d=e[a].disposalType===3?i.slice():null;if(te(i,s,n),o.push(i.slice()),e[a].disposalType===2)ee({typedArray:i,dx:s.dims.left,dy:s.dims.top,width:s.dims.width,height:s.dims.height,gifWidth:n.width});else if(e[a].disposalType===3){if(!d)throw Error(\"Disposal type 3 without previous frame\");i=d}else i=o[a].slice()}return{...n,loaded:!0,delays:e.map(a=>a.delay),frames:o}});var A=new Map;self.addEventListener(\"message\",t=>{let{type:r,src:e}=t.data||t;switch(r){case\"parse\":{if(!A.has(e)){let n=new AbortController,o={signal:n.signal};A.set(e,n),N(e,o).then(c=>{self.postMessage(Object.assign(c,{src:e}),c.frames.map(i=>i.buffer))}).catch(c=>{self.postMessage({src:e,error:c,loaded:!0})}).finally(()=>{A.delete(e)})}break}case\"cancel\":{A.has(e)&&(A.get(e).abort(),A.delete(e));break}default:break}});})();\n";
