{"repository": {"url": "https://github.com/remotion-dev/remotion/tree/main/packages/media-utils"}, "name": "@remotion/media-utils", "version": "4.0.324", "description": "Utilities for working with media files", "main": "dist/index.js", "sideEffects": false, "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/remotion-dev/remotion/issues"}, "dependencies": {"remotion": "4.0.324"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "devDependencies": {"eslint": "9.19.0", "@remotion/eslint-config-internal": "4.0.324"}, "keywords": ["remotion", "ffmpeg", "video", "react", "player"], "publishConfig": {"access": "public"}, "homepage": "https://www.remotion.dev/docs/media-utils", "scripts": {"formatting": "prettier --experimental-cli src --check", "lint": "eslint src", "make": "tsc -d"}}