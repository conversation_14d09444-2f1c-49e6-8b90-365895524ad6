import React from "react";
import {
  AbsoluteFill,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  spring,
  Easing,
  measureSpring,
} from "remotion";

interface AdvancedEngagementElementsProps {
  frame: number;
  fps: number;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
  author: string;
}

export const AdvancedEngagementElements: React.FC<AdvancedEngagementElementsProps> = ({
  frame,
  fps,
  colors,
  author
}) => {
  const { durationInFrames } = useVideoConfig();
  
  // Configurações de animação otimizadas
  const springConfig = {
    damping: 200,
    stiffness: 100,
    mass: 1,
  };
  
  // Animações para diferentes elementos com timing correto
  const likeAnimationDuration = measureSpring({ fps, config: springConfig });
  const likeAnimation = spring({
    frame: frame % (fps * 6), // Repete a cada 6 segundos
    fps,
    config: springConfig,
    durationInFrames: likeAnimationDuration,
  });
  
  const shareAnimation = spring({
    frame: (frame + fps * 2) % (fps * 8), // Deslocado 2 segundos
    fps,
    config: { ...springConfig, damping: 150 },
    durationInFrames: measureSpring({ fps, config: { ...springConfig, damping: 150 } }),
  });
  
  const commentAnimation = spring({
    frame: (frame + fps * 4) % (fps * 10), // Deslocado 4 segundos
    fps,
    config: { ...springConfig, damping: 180 },
    durationInFrames: measureSpring({ fps, config: { ...springConfig, damping: 180 } }),
  });
  
  // Floating emoji animation com interpolação correta
  const floatingCycle = frame % (fps * 4);
  const floatingEmoji = interpolate(
    floatingCycle,
    [0, fps * 4],
    [0, -200],
    { 
      extrapolateLeft: "clamp", 
      extrapolateRight: "clamp",
      easing: Easing.out(Easing.ease)
    }
  );
  
  // Opacidade com fases bem definidas
  const phase1 = fps * 0.5; // 0.5 segundos
  const phase2 = fps * 1;   // 1 segundo
  const phase3 = fps * 3;   // 3 segundos
  const phase4 = fps * 4;   // 4 segundos
  
  const floatingOpacity = interpolate(
    floatingCycle,
    [0, phase1, phase2, phase3, phase4],
    [0, 0.3, 1, 1, 0],
    { 
      extrapolateLeft: "clamp", 
      extrapolateRight: "clamp",
      easing: Easing.inOut(Easing.ease)
    }
  );
  
  // Progress bar calculation
  const progressPercentage = interpolate(
    frame,
    [0, durationInFrames],
    [0, 100],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );
  
  // Animação pulsante para trending
  const trendingPulse = interpolate(
    frame % (fps * 2),
    [0, fps, fps * 2],
    [1, 1.05, 1],
    { 
      extrapolateLeft: "clamp", 
      extrapolateRight: "clamp",
      easing: Easing.inOut(Easing.ease)
    }
  );
  
  // Animação de contador com efeito de contagem
  const counterAnimation = interpolate(
    frame,
    [0, fps * 2, fps * 4],
    [0, 1500, 2500],
    { 
      extrapolateLeft: "clamp", 
      extrapolateRight: "clamp",
      easing: Easing.out(Easing.ease)
    }
  );
  
  return (
    <AbsoluteFill>
      {/* Container principal para elementos de engajamento */}
      <div style={{ position: "relative", width: "100%", height: "100%" }}>
        
        {/* Botão de Like animado com contador dinâmico */}
        <div
          style={{
            position: "absolute",
            right: 30,
            top: "30%",
            transform: `scale(${1 + likeAnimation * 0.3})`,
            zIndex: 1000,
          }}
        >
          <div
            style={{
              width: 70,
              height: 70,
              borderRadius: "50%",
              background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              boxShadow: `0 10px 30px ${colors.primary}40, 0 4px 15px rgba(0,0,0,0.3)`,
              border: `3px solid ${colors.text}`,
              position: "relative",
              overflow: "hidden",
            }}
          >
            <div
              style={{
                position: "absolute",
                inset: 0,
                background: `linear-gradient(45deg, transparent, ${colors.text}20, transparent)`,
                animation: frame % 120 < 60 ? "shimmer 2s infinite" : "none",
              }}
            />
            <span style={{ fontSize: 32, filter: "drop-shadow(2px 2px 4px rgba(0,0,0,0.3))" }}>❤️</span>
          </div>
          <div
            style={{
              color: colors.text,
              fontSize: 16,
              fontWeight: "bold",
              textAlign: "center",
              marginTop: 12,
              textShadow: "2px 2px 4px rgba(0,0,0,0.8)",
              background: `${colors.primary}E6`,
              padding: "4px 8px",
              borderRadius: 12,
              border: `1px solid ${colors.text}40`,
            }}
          >
            {Math.floor(2500 + counterAnimation).toLocaleString()}
          </div>
        </div>
        
        {/* Botão de Share animado */}
        <div
          style={{
            position: "absolute",
            right: 30,
            top: "45%",
            transform: `scale(${1 + shareAnimation * 0.25})`,
            zIndex: 1000,
          }}
        >
          <div
            style={{
              width: 70,
              height: 70,
              borderRadius: "50%",
              background: `linear-gradient(135deg, ${colors.secondary}, ${colors.primary})`,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              boxShadow: `0 10px 30px ${colors.secondary}40, 0 4px 15px rgba(0,0,0,0.3)`,
              border: `3px solid ${colors.text}`,
              position: "relative",
            }}
          >
            <span style={{ fontSize: 32, filter: "drop-shadow(2px 2px 4px rgba(0,0,0,0.3))" }}>🚀</span>
          </div>
          <div
            style={{
              color: colors.text,
              fontSize: 16,
              fontWeight: "bold",
              textAlign: "center",
              marginTop: 12,
              textShadow: "2px 2px 4px rgba(0,0,0,0.8)",
              background: `${colors.secondary}E6`,
              padding: "4px 8px",
              borderRadius: 12,
              border: `1px solid ${colors.text}40`,
            }}
          >
            {Math.floor(847 + counterAnimation * 0.3).toLocaleString()}
          </div>
        </div>
        
        {/* Botão de Comment animado */}
        <div
          style={{
            position: "absolute",
            right: 30,
            top: "60%",
            transform: `scale(${1 + commentAnimation * 0.2})`,
            zIndex: 1000,
          }}
        >
          <div
            style={{
              width: 70,
              height: 70,
              borderRadius: "50%",
              background: `linear-gradient(135deg, ${colors.accent}, ${colors.secondary})`,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              boxShadow: `0 10px 30px ${colors.accent}40, 0 4px 15px rgba(0,0,0,0.3)`,
              border: `3px solid ${colors.text}`,
              position: "relative",
            }}
          >
            <span style={{ fontSize: 32, filter: "drop-shadow(2px 2px 4px rgba(0,0,0,0.3))" }}>💬</span>
          </div>
          <div
            style={{
              color: colors.text,
              fontSize: 16,
              fontWeight: "bold",
              textAlign: "center",
              marginTop: 12,
              textShadow: "2px 2px 4px rgba(0,0,0,0.8)",
              background: `${colors.accent}E6`,
              padding: "4px 8px",
              borderRadius: 12,
              border: `1px solid ${colors.text}40`,
            }}
          >
            {Math.floor(156 + counterAnimation * 0.1).toLocaleString()}
          </div>
        </div>
        
        {/* Floating emojis com animação suave */}
        <div
          style={{
            position: "absolute",
            left: 50,
            bottom: 450,
            transform: `translateY(${floatingEmoji}px)`,
            opacity: floatingOpacity,
            fontSize: 45,
            filter: "drop-shadow(3px 3px 6px rgba(0,0,0,0.5))",
          }}
        >
          🔥
        </div>
        
        <div
          style={{
            position: "absolute",
            left: 150,
            bottom: 400,
            transform: `translateY(${floatingEmoji - 30}px)`,
            opacity: floatingOpacity * 0.8,
            fontSize: 40,
            filter: "drop-shadow(3px 3px 6px rgba(0,0,0,0.5))",
          }}
        >
          ⚡
        </div>
        
        <div
          style={{
            position: "absolute",
            left: 250,
            bottom: 350,
            transform: `translateY(${floatingEmoji - 60}px)`,
            opacity: floatingOpacity * 0.6,
            fontSize: 35,
            filter: "drop-shadow(3px 3px 6px rgba(0,0,0,0.5))",
          }}
        >
          🎯
        </div>
        
        <div
          style={{
            position: "absolute",
            left: 350,
            bottom: 320,
            transform: `translateY(${floatingEmoji - 90}px)`,
            opacity: floatingOpacity * 0.4,
            fontSize: 30,
            filter: "drop-shadow(3px 3px 6px rgba(0,0,0,0.5))",
          }}
        >
          ✨
        </div>
        
        {/* Trending indicator com animação pulsante */}
        <div
          style={{
            position: "absolute",
            top: 60,
            right: 50,
            background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,
            padding: "12px 20px",
            borderRadius: 25,
            border: `3px solid ${colors.text}`,
            boxShadow: `0 8px 25px ${colors.primary}40, 0 4px 15px rgba(0,0,0,0.3)`,
            transform: `scale(${trendingPulse})`,
            backdropFilter: "blur(10px)",
          }}
        >
          <div
            style={{
              color: colors.text,
              fontSize: 18,
              fontWeight: "bold",
              textShadow: "2px 2px 4px rgba(0,0,0,0.5)",
              display: "flex",
              alignItems: "center",
              gap: 8,
            }}
          >
            <span style={{ fontSize: 20 }}>🔥</span>
            TRENDING
          </div>
        </div>
        
        {/* Progress bar profissional */}
        <div
          style={{
            position: "absolute",
            bottom: 30,
            left: 50,
            right: 50,
            height: 8,
            background: `linear-gradient(90deg, ${colors.text}20, ${colors.text}40, ${colors.text}20)`,
            borderRadius: 4,
            overflow: "hidden",
            boxShadow: "inset 0 2px 4px rgba(0,0,0,0.3)",
          }}
        >
          <div
            style={{
              height: "100%",
              background: `linear-gradient(90deg, ${colors.primary}, ${colors.accent}, ${colors.secondary})`,
              borderRadius: 4,
              width: `${Math.min(progressPercentage, 100)}%`,
              boxShadow: `0 0 15px ${colors.primary}80`,
              position: "relative",
            }}
          >
            <div
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: `linear-gradient(90deg, transparent, ${colors.text}40, transparent)`,
                animation: "shimmer 2s infinite",
              }}
            />
          </div>
        </div>
        
        {/* Indicador de visualizações */}
        <div
          style={{
            position: "absolute",
            top: 120,
            left: 50,
            background: `linear-gradient(135deg, ${colors.secondary}E6, ${colors.primary}E6)`,
            padding: "8px 16px",
            borderRadius: 20,
            border: `2px solid ${colors.text}40`,
            boxShadow: "0 4px 15px rgba(0,0,0,0.3)",
            backdropFilter: "blur(10px)",
          }}
        >
          <div
            style={{
              color: colors.text,
              fontSize: 14,
              fontWeight: "bold",
              textShadow: "1px 1px 2px rgba(0,0,0,0.7)",
              display: "flex",
              alignItems: "center",
              gap: 6,
            }}
          >
            <span style={{ fontSize: 16 }}>👁️</span>
            {Math.floor(15432 + counterAnimation * 2).toLocaleString()} visualizações
          </div>
        </div>
        
        {/* Indicador de tempo */}
        <div
          style={{
            position: "absolute",
            bottom: 50,
            right: 50,
            background: `${colors.primary}E6`,
            padding: "6px 12px",
            borderRadius: 15,
            border: `1px solid ${colors.text}40`,
            backdropFilter: "blur(5px)",
          }}
        >
          <div
            style={{
              color: colors.text,
              fontSize: 12,
              fontWeight: "600",
              textShadow: "1px 1px 2px rgba(0,0,0,0.7)",
            }}
          >
            {Math.floor(frame / fps)}s / {Math.floor(durationInFrames / fps)}s
          </div>
        </div>
        
      </div>
    </AbsoluteFill>
  );
};
