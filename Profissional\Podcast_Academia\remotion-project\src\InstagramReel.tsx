import React from "react";
import {
  AbsoluteFill,
  Video,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Audio,
  Artifact,
  staticFile,
  Sequence,
  spring,
  Easing,
  measureSpring,
} from "remotion";
import { PodcastClip } from "./components/PodcastClip";
import { DynamicSubtitles } from "./components/DynamicSubtitles";
import { AdvancedEngagementElements } from "./components/AdvancedEngagementElements";
import { BrandingOverlay } from "./components/BrandingOverlay";

interface InstagramReelProps {
  videoPath: string;
  startTime: number;
  duration: number;
  title: string;
  subtitle?: string;
  author: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
}

export const InstagramReel: React.FC<InstagramReelProps> = ({
  videoPath,
  startTime,
  duration,
  title,
  subtitle,
  author,
  colors
}) => {
  const frame = useCurrentFrame();
  const { fps, durationInFrames } = useVideoConfig();
  
  // Configurações de timing otimizadas
  const introEnd = fps * 2; // 2 segundos de intro
  const outroStart = durationInFrames - (fps * 3); // 3 segundos de outro
  
  // Configuração de spring otimizada
  const springConfig = {
    damping: 200,
    stiffness: 100,
    mass: 1,
  };
  
  // Animação de título com interpolação correta
  const titleFadeIn = fps * 0.5; // 0.5 segundos
  const titleHold = fps * 1.5; // 1.5 segundos
  const titleFadeOut = fps * 2; // 2 segundos
  
  const titleOpacity = interpolate(
    frame,
    [0, titleFadeIn, titleHold, titleFadeOut],
    [0, 1, 1, 0],
    { 
      extrapolateLeft: "clamp", 
      extrapolateRight: "clamp",
      easing: Easing.inOut(Easing.ease)
    }
  );
  
  // Animação de scale do título
  const titleScale = interpolate(
    frame,
    [0, titleFadeIn, titleHold],
    [0.8, 1, 1],
    { 
      extrapolateLeft: "clamp", 
      extrapolateRight: "clamp",
      easing: Easing.out(Easing.back(1.2))
    }
  );
  
  // Animação de entrada do vídeo com spring
  const videoScale = spring({
    frame: Math.max(0, frame - introEnd),
    fps,
    config: springConfig,
    durationInFrames: measureSpring({ fps, config: springConfig }),
  });
  
  // Animação de rotação sutil para elementos decorativos
  const rotationEffect = interpolate(
    frame,
    [0, durationInFrames],
    [0, 360],
    { 
      extrapolateLeft: "clamp", 
      extrapolateRight: "clamp",
      easing: Easing.linear
    }
  );
  
  // Animação de pulsação para elementos
  const pulseAnimation = interpolate(
    frame % (fps * 2),
    [0, fps, fps * 2],
    [1, 1.05, 1],
    { 
      extrapolateLeft: "clamp", 
      extrapolateRight: "clamp",
      easing: Easing.inOut(Easing.ease)
    }
  );
  
  // Geração de legendas SRT
  const subtitlesContent = generateSubtitles(videoPath, startTime, duration);
  
  return (
    <AbsoluteFill>
      {/* Background gradient animado */}
      <AbsoluteFill
        style={{
          background: `linear-gradient(135deg, ${colors.primary}, ${colors.secondary})`,
          animation: "gradientShift 10s ease-in-out infinite",
        }}
      />
      
      {/* Overlay de partículas */}
      <AbsoluteFill
        style={{
          background: `radial-gradient(circle at ${50 + Math.sin(frame * 0.02) * 20}% ${50 + Math.cos(frame * 0.02) * 20}%, ${colors.accent}20, transparent 70%)`,
          opacity: 0.6,
        }}
      />
      
      {/* Intro Title com animações profissionais */}
      <AbsoluteFill
        style={{
          opacity: titleOpacity,
          justifyContent: "center",
          alignItems: "center",
          padding: 60,
          zIndex: 1000,
        }}
      >
        <div
          style={{
            transform: `scale(${titleScale})`,
            textAlign: "center",
          }}
        >
          <div
            style={{
              fontSize: 72,
              fontWeight: "900",
              color: colors.text,
              textShadow: `0 0 30px ${colors.primary}, 4px 4px 8px rgba(0,0,0,0.8)`,
              lineHeight: 1.1,
              marginBottom: 20,
              background: `linear-gradient(135deg, ${colors.text}, ${colors.accent})`,
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              filter: "drop-shadow(2px 2px 4px rgba(0,0,0,0.5))",
            }}
          >
            {title}
          </div>
          {subtitle && (
            <div
              style={{
                fontSize: 40,
                color: colors.text,
                textAlign: "center",
                opacity: 0.9,
                textShadow: "3px 3px 6px rgba(0,0,0,0.7)",
                fontWeight: "600",
                background: `linear-gradient(135deg, ${colors.text}E6, ${colors.accent}E6)`,
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
              }}
            >
              {subtitle}
            </div>
          )}
        </div>
        
        {/* Elementos decorativos do título */}
        <div
          style={{
            position: "absolute",
            top: "20%",
            left: "10%",
            fontSize: 60,
            opacity: 0.3,
            transform: `rotate(${rotationEffect * 0.1}deg)`,
            filter: "blur(1px)",
          }}
        >
          ✨
        </div>
        
        <div
          style={{
            position: "absolute",
            bottom: "20%",
            right: "10%",
            fontSize: 60,
            opacity: 0.3,
            transform: `rotate(${-rotationEffect * 0.1}deg)`,
            filter: "blur(1px)",
          }}
        >
          🚀
        </div>
      </AbsoluteFill>
      
      {/* Video principal com animação suave */}
      <Sequence from={introEnd} durationInFrames={Math.max(0, durationInFrames - introEnd - (fps * 3))}>
        <PodcastClip
          videoPath={videoPath}
          startTime={startTime}
          duration={duration}
          scale={videoScale}
          colors={colors}
        />
      </Sequence>
      
      {/* Legendas dinâmicas */}
      <Sequence from={introEnd} durationInFrames={Math.max(0, durationInFrames - introEnd - (fps * 3))}>
        <DynamicSubtitles
          videoPath={videoPath}
          startTime={startTime}
          colors={colors}
        />
      </Sequence>
      
      {/* Elementos de engajamento avançados */}
      <Sequence from={introEnd + 30} durationInFrames={Math.max(0, durationInFrames - introEnd - (fps * 3) - 30)}>
        <AdvancedEngagementElements
          frame={frame}
          fps={fps}
          colors={colors}
          author={author}
        />
      </Sequence>
      
      {/* Branding overlay */}
      <BrandingOverlay
        frame={frame}
        author={author}
        colors={colors}
        position="bottom-right"
      />
      
      {/* Call to Action no final com animação */}
      <Sequence from={Math.max(0, outroStart)} durationInFrames={fps * 3}>
        <AbsoluteFill
          style={{
            justifyContent: "center",
            alignItems: "center",
            background: `linear-gradient(135deg, ${colors.accent}, ${colors.primary})`,
            padding: 80,
            transform: `scale(${pulseAnimation})`,
          }}
        >
          <div
            style={{
              textAlign: "center",
              zIndex: 1000,
            }}
          >
            <div
              style={{
                fontSize: 56,
                fontWeight: "900",
                color: colors.text,
                textShadow: `0 0 20px ${colors.primary}, 4px 4px 8px rgba(0,0,0,0.8)`,
                marginBottom: 30,
                filter: "drop-shadow(2px 2px 4px rgba(0,0,0,0.5))",
              }}
            >
              🚀 Quer aprender mais?
            </div>
            <div
              style={{
                fontSize: 36,
                color: colors.text,
                textShadow: "3px 3px 6px rgba(0,0,0,0.7)",
                opacity: 0.95,
                fontWeight: "600",
              }}
            >
              Siga @{author} para mais dicas!
            </div>
            
            {/* Botão de ação */}
            <div
              style={{
                marginTop: 40,
                padding: "16px 32px",
                background: `linear-gradient(135deg, ${colors.text}, ${colors.secondary})`,
                borderRadius: 30,
                border: `3px solid ${colors.accent}`,
                boxShadow: "0 8px 25px rgba(0,0,0,0.3)",
                display: "inline-block",
              }}
            >
              <div
                style={{
                  color: colors.primary,
                  fontSize: 24,
                  fontWeight: "bold",
                  textShadow: "1px 1px 2px rgba(0,0,0,0.3)",
                }}
              >
                👆 SEGUIR AGORA
              </div>
            </div>
          </div>
          
          {/* Elementos decorativos do CTA */}
          <div
            style={{
              position: "absolute",
              top: "10%",
              left: "10%",
              fontSize: 40,
              opacity: 0.4,
              transform: `rotate(${rotationEffect * 0.2}deg)`,
            }}
          >
            ⭐
          </div>
          
          <div
            style={{
              position: "absolute",
              bottom: "10%",
              right: "10%",
              fontSize: 40,
              opacity: 0.4,
              transform: `rotate(${-rotationEffect * 0.2}deg)`,
            }}
          >
            🎯
          </div>
        </AbsoluteFill>
      </Sequence>
      
      {/* Emit SRT file */}
      {frame === 0 && (
        <Artifact
          filename="reel-subtitles.srt"
          content={subtitlesContent}
        />
      )}
    </AbsoluteFill>
  );
};

// Função para gerar legendas SRT profissionais
function generateSubtitles(videoPath: string, startTime: number, duration: number): string {
  const subtitles = [
    "🎯 Como construir seu primeiro Agente IA",
    "Do ZERO em 90 dias com estratégias práticas",
    "Vou te mostrar o passo a passo completo",
    "Para transformar sua carreira com IA",
    "Começando pelo básico até o avançado",
    "Primeiro, você precisa entender os fundamentos",
    "Depois, escolher as ferramentas certas",
    "E finalmente, colocar em prática",
    "O segredo está na consistência",
    "E em seguir um método comprovado",
    "Que já funcionou para milhares de pessoas",
    "Você pode ser o próximo!",
    "Basta ter dedicação e foco",
    "Os resultados vão aparecer naturalmente",
    "🚀 Quer começar sua jornada?"
  ];
  
  const segmentDuration = duration / subtitles.length;
  let srtContent = "";
  
  subtitles.forEach((subtitle, index) => {
    const start = startTime + (index * segmentDuration);
    const end = startTime + ((index + 1) * segmentDuration);
    
    const startTime_srt = formatTime(start);
    const endTime_srt = formatTime(end);
    
    srtContent += `${index + 1}\n`;
    srtContent += `${startTime_srt} --> ${endTime_srt}\n`;
    srtContent += `${subtitle}\n\n`;
  });
  
  return srtContent;
}

function formatTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const millis = Math.floor((seconds % 1) * 1000);
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${millis.toString().padStart(3, '0')}`;
}
