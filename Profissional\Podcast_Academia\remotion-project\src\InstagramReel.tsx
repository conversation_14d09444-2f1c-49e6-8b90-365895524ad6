import React from "react";
import {
  AbsoluteFill,
  Video,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Audio,
  Artifact,
  staticFile,
  Sequence,
  spring,
  getRemotionEnvironment
} from "remotion";
import { PodcastClip } from "./components/PodcastClip";
import { DynamicSubtitles } from "./components/DynamicSubtitles";
import { EngagementElements } from "./components/EngagementElements";
import { BrandingOverlay } from "./components/BrandingOverlay";

interface InstagramReelProps {
  videoPath: string;
  startTime: number;
  duration: number;
  title: string;
  subtitle?: string;
  author: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
}

export const InstagramReel: React.FC<InstagramReelProps> = ({
  videoPath,
  startTime,
  duration,
  title,
  subtitle,
  author,
  colors
}) => {
  const frame = useCurrentFrame();
  const { fps, durationInFrames } = useVideoConfig();
  
  // Configurações de timing
  const introEnd = fps * 2; // 2 segundos de intro
  const outroStart = durationInFrames - (fps * 3); // 3 segundos de outro
  
  // Animações corrigidas para evitar valores monotônicos
  const titleOpacity = interpolate(
    frame,
    [0, 30, introEnd - 30, introEnd],
    [0, 1, 1, 0],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );
  
  const videoScale = spring({
    frame: Math.max(0, frame - introEnd),
    fps,
    config: {
      damping: 20,
      stiffness: 100,
      mass: 1
    }
  });
  
  // Geração de legendas SRT
  const subtitlesContent = generateSubtitles(videoPath, startTime, duration);
  
  return (
    <AbsoluteFill>
      {/* Background gradient */}
      <AbsoluteFill
        style={{
          background: `linear-gradient(135deg, ${colors.primary}, ${colors.secondary})`,
        }}
      />
      
      {/* Intro Title */}
      <AbsoluteFill
        style={{
          opacity: titleOpacity,
          justifyContent: "center",
          alignItems: "center",
          padding: 40,
        }}
      >
        <div
          style={{
            fontSize: 64,
            fontWeight: "bold",
            color: colors.text,
            textAlign: "center",
            textShadow: "2px 2px 4px rgba(0,0,0,0.5)",
            lineHeight: 1.2,
          }}
        >
          {title}
        </div>
        {subtitle && (
          <div
            style={{
              fontSize: 36,
              color: colors.text,
              textAlign: "center",
              marginTop: 20,
              opacity: 0.9,
            }}
          >
            {subtitle}
          </div>
        )}
      </AbsoluteFill>
      
      {/* Video principal */}
      <Sequence from={introEnd} durationInFrames={durationInFrames - introEnd - (fps * 3)}>
        <PodcastClip
          videoPath={videoPath}
          startTime={startTime}
          duration={duration}
          scale={videoScale}
          colors={colors}
        />
      </Sequence>
      
      {/* Legendas dinâmicas */}
      <Sequence from={introEnd} durationInFrames={durationInFrames - introEnd - (fps * 3)}>
        <DynamicSubtitles
          videoPath={videoPath}
          startTime={startTime}
          colors={colors}
        />
      </Sequence>
      
      {/* Elementos de engajamento */}
      <Sequence from={introEnd + 30} durationInFrames={durationInFrames - introEnd - (fps * 3) - 30}>
        <EngagementElements
          frame={frame}
          fps={fps}
          colors={colors}
          author={author}
        />
      </Sequence>
      
      {/* Branding overlay */}
      <BrandingOverlay
        frame={frame}
        author={author}
        colors={colors}
        position="bottom-right"
      />
      
      {/* Call to Action no final */}
      <Sequence from={outroStart} durationInFrames={fps * 3}>
        <AbsoluteFill
          style={{
            justifyContent: "center",
            alignItems: "center",
            background: `linear-gradient(135deg, ${colors.accent}, ${colors.primary})`,
            padding: 60,
          }}
        >
          <div
            style={{
              fontSize: 48,
              fontWeight: "bold",
              color: colors.text,
              textAlign: "center",
              textShadow: "2px 2px 4px rgba(0,0,0,0.5)",
            }}
          >
            🚀 Quer aprender mais?
          </div>
          <div
            style={{
              fontSize: 32,
              color: colors.text,
              textAlign: "center",
              marginTop: 30,
              opacity: 0.9,
            }}
          >
            Siga @{author} para mais dicas!
          </div>
        </AbsoluteFill>
      </Sequence>
      
      {/* Emit SRT file */}
      {frame === 0 && (
        <Artifact
          filename="reel-subtitles.srt"
          content={subtitlesContent}
        />
      )}
    </AbsoluteFill>
  );
};

// Função para gerar legendas SRT
function generateSubtitles(videoPath: string, startTime: number, duration: number): string {
  // Esta função seria expandida para usar whisper ou similar para transcrição automática
  // Por agora, retorna um template básico
  return `1
00:00:00,000 --> 00:00:04,000
🎯 Como construir seu primeiro Agente IA

2
00:00:04,000 --> 00:00:08,000
Do ZERO em 90 dias com Sami

3
00:00:08,000 --> 00:00:12,000
Estratégias práticas e comprovadas

4
00:00:12,000 --> 00:00:16,000
Para transformar sua carreira

5
00:00:16,000 --> 00:00:20,000
Com Inteligência Artificial

6
00:00:20,000 --> 00:00:24,000
Primeiro, você precisa entender os fundamentos

7
00:00:24,000 --> 00:00:28,000
Depois, escolher as ferramentas certas

8
00:00:28,000 --> 00:00:32,000
E finalmente, colocar em prática

9
00:00:32,000 --> 00:00:36,000
O segredo está na consistência

10
00:00:36,000 --> 00:00:40,000
E em seguir um método comprovado`;
}
