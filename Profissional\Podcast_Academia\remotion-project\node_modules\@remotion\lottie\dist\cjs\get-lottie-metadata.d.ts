import type { LottieAnimationData } from './types';
type LottieMetadata = {
    fps: number;
    durationInSeconds: number;
    durationInFrames: number;
    width: number;
    height: number;
};
/**
 * @description Get the basic metadata such as dimensions, duration and framerate of a Lottie animation.
 * @see [Documentation](https://www.remotion.dev/docs/lottie/getlottiemetadata)
 */
export declare const getLottieMetadata: (animationData: LottieAnimationData) => LottieMetadata | null;
export {};
