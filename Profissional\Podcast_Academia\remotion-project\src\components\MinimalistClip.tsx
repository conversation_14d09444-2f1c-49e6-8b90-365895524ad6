import React from "react";
import {
  AbsoluteFill,
  Video,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Audio,
  staticFile
} from "remotion";

interface MinimalistClipProps {
  videoPath: string;
  startTime: number;
  duration: number;
  scale: number;
  title?: string;
  subtitle?: string;
  author?: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
}

export const MinimalistClip: React.FC<MinimalistClipProps> = ({
  videoPath,
  startTime,
  duration,
  scale,
  title = "Agente IA",
  subtitle = "Construa em 90 dias",
  author = "Academia",
  colors,
}) => {
  const frame = useCurrentFrame();
  const { durationInFrames, fps } = useVideoConfig();

  // Animações sutis e elegantes
  const fadeIn = interpolate(
    frame,
    [0, 30],
    [0, 1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  const fadeOut = interpolate(
    frame,
    [durationInFrames - 30, durationInFrames],
    [1, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  const opacity = Math.min(fadeIn, fadeOut);

  // Movimento sutil do título
  const titleY = interpolate(
    frame,
    [0, 60],
    [20, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  // Progress bar minimalista
  const progress = interpolate(
    frame,
    [0, durationInFrames],
    [0, 100],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  return (
    <AbsoluteFill>
      {/* Áudio sincronizado - corrigido */}
      <Audio
        src={staticFile("audio_extracted.mp3")}
        startFrom={startTime * fps}
        endAt={(startTime + duration) * fps}
        volume={0.8}
      />

      {/* Background minimalista */}
      <div
        style={{
          width: "100%",
          height: "100%",
          background: colors.background,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          padding: "40px",
          opacity,
        }}
      >
        {/* Container do vídeo - design limpo */}
        <div
          style={{
            width: "85%",
            height: "65%",
            borderRadius: 12,
            overflow: "hidden",
            boxShadow: "0 8px 32px rgba(0,0,0,0.12)",
            border: `1px solid ${colors.accent}20`,
            position: "relative",
            background: "#000",
          }}
        >
          <Video
            src={staticFile(videoPath)}
            startFrom={startTime * fps}
            endAt={(startTime + duration) * fps}
            style={{
              width: "100%",
              height: "100%",
              objectFit: "cover",
            }}
            muted={true}
            volume={0}
          />

          {/* Overlay sutil */}
          <div
            style={{
              position: "absolute",
              bottom: 0,
              left: 0,
              right: 0,
              height: "30%",
              background: "linear-gradient(transparent, rgba(0,0,0,0.4))",
              pointerEvents: "none",
            }}
          />
        </div>

        {/* Título minimalista */}
        <div
          style={{
            marginTop: "30px",
            textAlign: "center",
            transform: `translateY(${titleY}px)`,
          }}
        >
          <h1
            style={{
              fontSize: "2.2rem",
              fontWeight: "600",
              color: colors.text,
              margin: "0 0 8px 0",
              fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif",
              letterSpacing: "-0.02em",
              lineHeight: "1.2",
            }}
          >
            {title}
          </h1>
          
          {subtitle && (
            <p
              style={{
                fontSize: "1.1rem",
                fontWeight: "400",
                color: colors.secondary,
                margin: "0 0 20px 0",
                fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif",
                opacity: 0.8,
              }}
            >
              {subtitle}
            </p>
          )}
        </div>

        {/* Progress bar minimalista */}
        <div
          style={{
            position: "absolute",
            bottom: 20,
            left: 40,
            right: 40,
            height: 2,
            background: `${colors.accent}20`,
            borderRadius: 1,
          }}
        >
          <div
            style={{
              width: `${progress}%`,
              height: "100%",
              background: colors.accent,
              borderRadius: 1,
              transition: "width 0.1s ease-out",
            }}
          />
        </div>

        {/* Branding sutil */}
        <div
          style={{
            position: "absolute",
            top: 30,
            right: 30,
            padding: "8px 16px",
            background: `${colors.primary}08`,
            borderRadius: 20,
            border: `1px solid ${colors.primary}15`,
          }}
        >
          <span
            style={{
              fontSize: "0.9rem",
              fontWeight: "500",
              color: colors.primary,
              fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif",
            }}
          >
            @{author}
          </span>
        </div>

        {/* CTA minimalista - aparece nos últimos segundos */}
        {frame > durationInFrames - 120 && (
          <div
            style={{
              position: "absolute",
              bottom: 80,
              left: "50%",
              transform: "translateX(-50%)",
              padding: "12px 24px",
              background: colors.primary,
              borderRadius: 25,
              opacity: interpolate(
                frame,
                [durationInFrames - 120, durationInFrames - 90],
                [0, 1],
                { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
              ),
            }}
          >
            <span
              style={{
                fontSize: "1rem",
                fontWeight: "500",
                color: colors.background,
                fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif",
              }}
            >
              Saiba mais
            </span>
          </div>
        )}
      </div>
    </AbsoluteFill>
  );
};
