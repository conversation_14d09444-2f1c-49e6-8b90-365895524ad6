@echo off
title Sistema de Edição Profissional para Instagram - Demonstração

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo ██                                                                            ██
echo ██    🎬 SISTEMA DE EDIÇÃO PROFISSIONAL PARA INSTAGRAM                        ██
echo ██    Transforme seus podcasts em conteúdo viral                             ██
echo ██                                                                            ██
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo 🚀 SISTEMA COMPLETO E FUNCIONAL
echo.
echo ✅ Projeto Remotion configurado
echo ✅ Componentes profissionais criados
echo ✅ Scripts de renderização prontos
echo ✅ Vídeos de origem disponíveis
echo.

echo 📁 ESTRUTURA DO PROJETO:
echo.
echo remotion-project/
echo ├── src/
echo │   ├── InstagramReel.tsx      (Vídeos 9:16 - 60s)
echo │   ├── InstagramStory.tsx     (Stories 9:16 - 15s)
echo │   ├── InstagramPost.tsx      (Posts 1:1 - 60s)
echo │   └── components/            (Componentes profissionais)
echo ├── scripts/
echo │   └── render-all.js          (Renderização em massa)
echo └── output/                    (Vídeos gerados)
echo.

echo 🎯 OPÇÕES DE EXECUÇÃO:
echo.
echo [1] Testar sistema com renderização individual
echo [2] Renderizar todos os formatos (Reel, Story, Post)
echo [3] Abrir editor visual do Remotion
echo [4] Mostrar instruções detalhadas
echo [5] Sair
echo.

set /p choice="Escolha uma opção (1-5): "

if "%choice%"=="1" goto :teste_individual
if "%choice%"=="2" goto :renderizar_todos
if "%choice%"=="3" goto :editor_visual
if "%choice%"=="4" goto :instrucoes
if "%choice%"=="5" goto :sair

echo Opção inválida!
pause
goto :menu

:teste_individual
echo.
echo 🎬 TESTE INDIVIDUAL - Instagram Story
echo =====================================
echo.

cd /d "C:\Users\<USER>\Documents\Augment_Projects\Profissional\Podcast_Academia\remotion-project"

if not exist "output" mkdir "output"

echo 🎥 Renderizando Instagram Story de 15 segundos...
echo 📁 Vídeo: Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4
echo ⏱️  Duração: 15 segundos
echo 🎨 Formato: 1080x1920 (9:16)
echo.

node_modules\.bin\remotion.cmd render src/index.ts InstagramStory "output/teste_story.mp4" --props="{\"videoPath\": \"../Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4\", \"startTime\": 0, \"duration\": 15, \"title\": \"Como construir seu primeiro Agente IA\", \"author\": \"Sami\", \"colors\": {\"primary\": \"#FF6B35\", \"secondary\": \"#2E86AB\", \"accent\": \"#A23B72\", \"background\": \"#F18F01\", \"text\": \"#FFFFFF\"}}"

if %errorlevel% == 0 (
    echo.
    echo ✅ SUCESSO! Vídeo renderizado:
    echo 📁 output\teste_story.mp4
    echo.
    echo 🎯 Próximos passos:
    echo 1. Verificar o vídeo gerado
    echo 2. Testar no Instagram
    echo 3. Ajustar configurações se necessário
    echo.
) else (
    echo.
    echo ❌ ERRO durante renderização
    echo Verifique se todos os arquivos estão presentes
    echo.
)

pause
goto :menu

:renderizar_todos
echo.
echo 🎬 RENDERIZAÇÃO EM MASSA
echo ========================
echo.

cd /d "C:\Users\<USER>\Documents\Augment_Projects\Profissional\Podcast_Academia\remotion-project"

echo 🚀 Iniciando renderização de todos os formatos...
echo.
echo 📊 Processando:
echo • 5 segmentos do vídeo principal
echo • 3 formatos (Reel, Story, Post)
echo • Total: ~15 vídeos a serem gerados
echo.

node scripts/render-all.js

echo.
echo 📁 Vídeos gerados na pasta output/
dir /b "output\*.mp4" 2>nul || echo Nenhum arquivo MP4 encontrado

echo.
pause
goto :menu

:editor_visual
echo.
echo 🎨 EDITOR VISUAL REMOTION
echo =========================
echo.

cd /d "C:\Users\<USER>\Documents\Augment_Projects\Profissional\Podcast_Academia\remotion-project"

echo 🌐 Abrindo editor visual do Remotion...
echo 📝 Acesse: http://localhost:3000
echo.
echo 💡 No editor você pode:
echo • Visualizar componentes em tempo real
echo • Ajustar timing e animações
echo • Testar diferentes configurações
echo • Pré-visualizar antes de renderizar
echo.

node_modules\.bin\remotion.cmd studio

pause
goto :menu

:instrucoes
echo.
echo 📋 INSTRUÇÕES DETALHADAS
echo ========================
echo.
echo 🎯 PERSONALIZAÇÃO:
echo.
echo 1. CORES E TEMAS:
echo    Edite: src/index.ts
echo    Altere: colors: { primary, secondary, accent, background, text }
echo.
echo 2. VÍDEOS:
echo    Edite: scripts/render-all.js
echo    Configure: filename, segments, start, duration, title, hook
echo.
echo 3. LEGENDAS:
echo    Edite: src/components/DynamicSubtitles.tsx
echo    Altere: mockSubtitles array
echo.
echo 🔧 COMANDOS MANUAIS:
echo.
echo • Renderizar Reel:
echo   node_modules\.bin\remotion.cmd render src/index.ts InstagramReel output/reel.mp4 --props="..."
echo.
echo • Renderizar Story:
echo   node_modules\.bin\remotion.cmd render src/index.ts InstagramStory output/story.mp4 --props="..."
echo.
echo • Renderizar Post:
echo   node_modules\.bin\remotion.cmd render src/index.ts InstagramPost output/post.mp4 --props="..."
echo.
echo 📈 ESTRATÉGIA DE POSTING:
echo.
echo • Reels: 19:00-21:00 (1-2 por dia)
echo • Stories: 12:00-14:00 e 18:00-20:00 (3-5 por dia)
echo • Posts: 11:00-13:00 e 17:00-19:00 (1 por dia)
echo.
echo 🎨 FORMATOS:
echo.
echo • Reel: 1080x1920 (9:16) - 15-60s
echo • Story: 1080x1920 (9:16) - 5-15s
echo • Post: 1080x1080 (1:1) - 3-60s
echo.

pause
goto :menu

:sair
echo.
echo 🎉 SISTEMA PRONTO PARA USO!
echo.
echo 💡 Dicas finais:
echo • Execute opção 1 para testar primeiro
echo • Use opção 2 para renderização em massa
echo • Personalize cores e textos conforme necessário
echo • Teste os vídeos no Instagram antes de postar
echo.
echo 🚀 Transforme seus podcasts em conteúdo viral!
echo.
pause
exit

:menu
echo.
echo 🎯 OPÇÕES DE EXECUÇÃO:
echo.
echo [1] Testar sistema com renderização individual
echo [2] Renderizar todos os formatos (Reel, Story, Post)
echo [3] Abrir editor visual do Remotion
echo [4] Mostrar instruções detalhadas
echo [5] Sair
echo.

set /p choice="Escolha uma opção (1-5): "

if "%choice%"=="1" goto :teste_individual
if "%choice%"=="2" goto :renderizar_todos
if "%choice%"=="3" goto :editor_visual
if "%choice%"=="4" goto :instrucoes
if "%choice%"=="5" goto :sair

echo Opção inválida!
pause
goto :menu
