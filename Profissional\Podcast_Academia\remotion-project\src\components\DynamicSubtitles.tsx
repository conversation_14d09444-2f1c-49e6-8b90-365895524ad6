import React from "react";
import {
  AbsoluteFill,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
} from "remotion";

interface DynamicSubtitlesProps {
  videoPath: string;
  startTime: number;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
}

// Simulação de legendas sincronizadas (seria substituído por transcrição real)
const mockSubtitles = [
  { start: 0, end: 4, text: "🎯 Como construir seu primeiro Agente IA" },
  { start: 4, end: 8, text: "Do ZERO em 90 dias com estratégias práticas" },
  { start: 8, end: 12, text: "Vou te mostrar o passo a passo completo" },
  { start: 12, end: 16, text: "Para transformar sua carreira com IA" },
  { start: 16, end: 20, text: "Começando pelo básico até o avançado" },
  { start: 20, end: 24, text: "<PERSON><PERSON>, você precisa entender os fundamentos" },
  { start: 24, end: 28, text: "Depo<PERSON>, escolher as ferramentas certas" },
  { start: 28, end: 32, text: "E finalmente, colocar em prática" },
  { start: 32, end: 36, text: "O segredo está na consistência" },
  { start: 36, end: 40, text: "E em seguir um método comprovado" },
  { start: 40, end: 44, text: "Que já funcionou para centenas de pessoas" },
  { start: 44, end: 48, text: "Você pode ser a próxima!" },
  { start: 48, end: 52, text: "Basta ter dedicação e foco" },
  { start: 52, end: 56, text: "Os resultados vão aparecer naturalmente" },
  { start: 56, end: 60, text: "🚀 Quer começar sua jornada?" },
];

export const DynamicSubtitles: React.FC<DynamicSubtitlesProps> = ({
  videoPath,
  startTime,
  colors
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  // Calcular o tempo atual em segundos
  const currentTime = frame / fps;
  
  // Encontrar a legenda atual
  const currentSubtitle = mockSubtitles.find(
    (sub) => currentTime >= sub.start && currentTime < sub.end
  );
  
  if (!currentSubtitle) {
    return null;
  }
  
  // Calcular progresso da palavra atual
  const wordProgress = interpolate(
    currentTime,
    [currentSubtitle.start, currentSubtitle.end],
    [0, 1],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );
  
  // Animação de entrada - corrigida para evitar valores monotônicos
  const entryDuration = 0.3;
  const exitDuration = 0.3;
  const subtitleDuration = currentSubtitle.end - currentSubtitle.start;
  
  const subtitleOpacity = interpolate(
    currentTime - currentSubtitle.start,
    [0, entryDuration, Math.max(entryDuration + 0.1, subtitleDuration - exitDuration), subtitleDuration],
    [0, 1, 1, 0],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );
  
  // Animação de escala
  const subtitleScale = interpolate(
    currentTime - currentSubtitle.start,
    [0, entryDuration],
    [0.8, 1],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );
  
  // Dividir texto em palavras para efeito de highlight
  const words = currentSubtitle.text.split(" ");
  const currentWordIndex = Math.floor(wordProgress * words.length);
  
  return (
    <AbsoluteFill>
      {/* Container das legendas */}
      <div
        style={{
          position: "absolute",
          bottom: 200,
          left: 40,
          right: 40,
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          opacity: subtitleOpacity,
          transform: `scale(${subtitleScale})`,
        }}
      >
        <div
          style={{
            background: `linear-gradient(135deg, ${colors.primary}E6, ${colors.secondary}E6)`,
            borderRadius: 20,
            padding: "20px 30px",
            boxShadow: "0 10px 30px rgba(0,0,0,0.3)",
            border: `2px solid ${colors.accent}`,
            backdropFilter: "blur(10px)",
            maxWidth: "90%",
          }}
        >
          <div
            style={{
              fontSize: 42,
              fontWeight: "bold",
              color: colors.text,
              textAlign: "center",
              textShadow: "2px 2px 4px rgba(0,0,0,0.5)",
              lineHeight: 1.3,
              display: "flex",
              flexWrap: "wrap",
              justifyContent: "center",
              gap: "8px",
            }}
          >
            {words.map((word, index) => (
              <span
                key={index}
                style={{
                  color: index <= currentWordIndex ? colors.text : `${colors.text}80`,
                  backgroundColor: index === currentWordIndex ? colors.accent : "transparent",
                  padding: index === currentWordIndex ? "2px 8px" : "2px 0",
                  borderRadius: index === currentWordIndex ? "8px" : "0",
                  transition: "all 0.3s ease",
                }}
              >
                {word}
              </span>
            ))}
          </div>
        </div>
      </div>
      
      {/* Indicador de progresso */}
      <div
        style={{
          position: "absolute",
          bottom: 160,
          left: 40,
          right: 40,
          height: 4,
          background: `${colors.text}30`,
          borderRadius: 2,
          opacity: subtitleOpacity,
        }}
      >
        <div
          style={{
            height: "100%",
            background: `linear-gradient(90deg, ${colors.primary}, ${colors.accent})`,
            borderRadius: 2,
            width: `${wordProgress * 100}%`,
            transition: "width 0.1s ease",
          }}
        />
      </div>
    </AbsoluteFill>
  );
};
