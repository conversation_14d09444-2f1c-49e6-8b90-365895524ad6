import React from "react";
import {
  AbsoluteFill,
  useCurrent<PERSON>rame,
  useVideoConfig,
  interpolate,
} from "remotion";

interface DynamicSubtitlesProps {
  videoPath: string;
  startTime: number;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
}

// Simulação de legendas sincronizadas (seria substituído por transcrição real)
const mockSubtitles = [
  { start: 0, end: 3, text: "🎯 Como construir seu primeiro Agente IA" },
  { start: 3, end: 6, text: "Do ZERO em 90 dias com estratégias práticas" },
  { start: 6, end: 9, text: "Vou te mostrar o passo a passo completo" },
  { start: 9, end: 12, text: "Para transformar sua carreira com IA" },
  { start: 12, end: 15, text: "Começando pelo básico até o avançado" },
  { start: 15, end: 18, text: "<PERSON><PERSON>, você precisa entender os fundamentos" },
  { start: 18, end: 21, text: "Depo<PERSON>, escolher as ferramentas certas" },
  { start: 21, end: 24, text: "E finalmente, colocar em prática" },
  { start: 24, end: 27, text: "O segredo está na consistência" },
  { start: 27, end: 30, text: "E em seguir um método comprovado" },
  { start: 30, end: 33, text: "Que já funcionou para centenas de pessoas" },
  { start: 33, end: 36, text: "Você pode ser a próxima!" },
  { start: 36, end: 39, text: "Basta ter dedicação e foco" },
  { start: 39, end: 42, text: "Os resultados vão aparecer naturalmente" },
  { start: 42, end: 45, text: "E sua vida pode mudar completamente" },
  { start: 45, end: 48, text: "Como aconteceu comigo" },
  { start: 48, end: 51, text: "E com muitos outros alunos" },
  { start: 51, end: 54, text: "Que hoje faturam milhares por mês" },
  { start: 54, end: 57, text: "Trabalhando com Inteligência Artificial" },
  { start: 57, end: 60, text: "🚀 Quer começar sua jornada?" },
];

export const DynamicSubtitles: React.FC<DynamicSubtitlesProps> = ({
  videoPath,
  startTime,
  colors
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  // Calcular o tempo atual em segundos
  const currentTime = frame / fps;
  
  // Encontrar a legenda atual
  const currentSubtitle = mockSubtitles.find(
    (sub) => currentTime >= sub.start && currentTime < sub.end
  );
  
  if (!currentSubtitle) {
    return null;
  }
  
  // Calcular progresso da palavra atual
  const wordProgress = interpolate(
    currentTime,
    [currentSubtitle.start, currentSubtitle.end],
    [0, 1],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );
  
  // Animação de entrada
  const subtitleOpacity = interpolate(
    currentTime - currentSubtitle.start,
    [0, 0.2, currentSubtitle.end - currentSubtitle.start - 0.2, currentSubtitle.end - currentSubtitle.start],
    [0, 1, 1, 0],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );
  
  // Animação de escala
  const subtitleScale = interpolate(
    currentTime - currentSubtitle.start,
    [0, 0.3],
    [0.8, 1],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );
  
  // Dividir texto em palavras para efeito de highlight
  const words = currentSubtitle.text.split(" ");
  const currentWordIndex = Math.floor(wordProgress * words.length);
  
  return (
    <AbsoluteFill>
      {/* Container das legendas */}
      <div
        style={{
          position: "absolute",
          bottom: 200,
          left: 40,
          right: 40,
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          opacity: subtitleOpacity,
          transform: `scale(${subtitleScale})`,
        }}
      >
        <div
          style={{
            background: `linear-gradient(135deg, ${colors.primary}E6, ${colors.secondary}E6)`,
            borderRadius: 20,
            padding: "20px 30px",
            boxShadow: "0 10px 30px rgba(0,0,0,0.3)",
            border: `2px solid ${colors.accent}`,
            backdropFilter: "blur(10px)",
            maxWidth: "90%",
          }}
        >
          <div
            style={{
              fontSize: 42,
              fontWeight: "bold",
              color: colors.text,
              textAlign: "center",
              textShadow: "2px 2px 4px rgba(0,0,0,0.5)",
              lineHeight: 1.3,
              display: "flex",
              flexWrap: "wrap",
              justifyContent: "center",
              gap: "8px",
            }}
          >
            {words.map((word, index) => (
              <span
                key={index}
                style={{
                  color: index <= currentWordIndex ? colors.text : `${colors.text}80`,
                  backgroundColor: index === currentWordIndex ? colors.accent : "transparent",
                  padding: index === currentWordIndex ? "2px 8px" : "2px 0",
                  borderRadius: index === currentWordIndex ? "8px" : "0",
                  transition: "all 0.3s ease",
                }}
              >
                {word}
              </span>
            ))}
          </div>
        </div>
      </div>
      
      {/* Indicador de progresso */}
      <div
        style={{
          position: "absolute",
          bottom: 160,
          left: 40,
          right: 40,
          height: 4,
          background: `${colors.text}30`,
          borderRadius: 2,
          opacity: subtitleOpacity,
        }}
      >
        <div
          style={{
            height: "100%",
            background: `linear-gradient(90deg, ${colors.primary}, ${colors.accent})`,
            borderRadius: 2,
            width: `${wordProgress * 100}%`,
            transition: "width 0.1s ease",
          }}
        />
      </div>
    </AbsoluteFill>
  );
};
