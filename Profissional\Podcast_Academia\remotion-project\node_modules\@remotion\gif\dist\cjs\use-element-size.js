"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useElementSize = exports.updateAllElementsSizes = void 0;
const react_1 = require("react");
let elementSizeHooks = [];
const updateAllElementsSizes = () => {
    for (const listener of elementSizeHooks) {
        listener();
    }
};
exports.updateAllElementsSizes = updateAllElementsSizes;
const useElementSize = (ref) => {
    const [size, setSize] = (0, react_1.useState)(null);
    const observer = (0, react_1.useMemo)(() => {
        if (typeof ResizeObserver === 'undefined') {
            return null;
        }
        return new ResizeObserver((entries) => {
            // The contentRect returns the width without any `scale()`'s being applied. The height is wrong
            const { contentRect } = entries[0];
            // The clientRect returns the size with `scale()` being applied.
            const newSize = entries[0].target.getClientRects();
            if (!(newSize === null || newSize === void 0 ? void 0 : newSize[0])) {
                setSize(null);
                return;
            }
            const probableCssParentScale = contentRect.width === 0 ? 1 : newSize[0].width / contentRect.width;
            const width = probableCssParentScale > 0
                ? newSize[0].width * (1 / probableCssParentScale)
                : newSize[0].width;
            const height = probableCssParentScale > 0
                ? newSize[0].height * (1 / probableCssParentScale)
                : newSize[0].height;
            setSize({
                width,
                height,
            });
        });
    }, []);
    const updateSize = (0, react_1.useCallback)(() => {
        if (!ref.current) {
            return;
        }
        const rect = ref.current.getClientRects();
        if (!rect[0]) {
            setSize(null);
            return;
        }
        setSize({
            width: rect[0].width,
            height: rect[0].height,
        });
    }, [ref]);
    (0, react_1.useEffect)(() => {
        if (!observer) {
            return;
        }
        updateSize();
        const { current } = ref;
        if (ref.current) {
            observer.observe(ref.current);
        }
        return () => {
            if (current) {
                observer.unobserve(current);
            }
        };
    }, [observer, ref, updateSize]);
    (0, react_1.useEffect)(() => {
        elementSizeHooks.push(updateSize);
        return () => {
            elementSizeHooks = elementSizeHooks.filter((e) => e !== updateSize);
        };
    }, [updateSize]);
    return size;
};
exports.useElementSize = useElementSize;
