<p align="center">
    <img width="20%" src="https://raw.githubusercontent.com/stefanwittwer/remotion-animated/main/sites/docs/static/img/remotion-animated.svg" alt="Remotion Animated Logo" />
    <h1 align="center">Remotion Animated</h1>
</p>
<p align="center">
    A delightful way to animate objects in <a href="https://www.remotion.dev">Remotion</a>.
</p>

<p align="center">
  <a href="https://github.com/stefanwittwer/remotion-animated/blob/main/LICENSE.md">
    <img src="https://img.shields.io/github/license/stefanwittwer/remotion-animated" alt="License" />
  </a>
  <a href="https://www.npmjs.com/package/remotion-animated">
    <img src="https://img.shields.io/npm/v/remotion-animated" alt="remotion-animated on npm" />
  </a>
  <img src="https://img.shields.io/badge/TypeScript-%23007ACC.svg?logo=typescript&logoColor=white" alt="TypeScript" />
</p>

## What is Remotion Animated?

The idea behind Remotion Animated is to simplify and speed up the creation of simple animations in your Remotion projects, by:

- Moving the animation logic fully into the JSX, using the `<Animated />` component;
- Providing defaults that make animations look great out-of-the-box;
- Making it easy to chain multiple timed animations.

## Documentation and Examples

See the [documentation and examples](https://www.remotion-animated.dev/) for this package on the package website.

## Installation

To get started with this package in your Remotion project, install `remotion-animated` using the package manager of your choice:

**Install using npm**

```
npm i remotion-animated
```

**Install using yarn**

```
yarn add remotion-animated
```

## License

This package and its source code is available under the MIT license. [Read the full license terms here.](https://github.com/stefanwittwer/remotion-animated/blob/main/LICENSE.md)
