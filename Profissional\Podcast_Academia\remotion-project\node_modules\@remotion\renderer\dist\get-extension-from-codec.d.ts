import type { Codec } from './codec';
import type { FileExtension } from './file-extensions';
import type { AudioCodec } from './options/audio-codec';
export declare const getFileExtensionFromCodec: <T extends Codec>(codec: T, audioCodec: AudioCodec | null) => FileExtension;
export declare const makeFileExtensionMap: () => Record<string, ("h264" | "h265" | "vp8" | "vp9" | "mp3" | "aac" | "wav" | "prores" | "h264-mkv" | "h264-ts" | "gif")[]>;
export declare const defaultCodecsForFileExtension: Record<FileExtension, Codec>;
