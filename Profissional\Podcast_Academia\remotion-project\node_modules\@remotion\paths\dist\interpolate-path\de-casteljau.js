"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.decasteljau = decasteljau;
/**
 * de <PERSON><PERSON>jau's algorithm for drawing and splitting bezier curves.
 * Inspired by https://pomax.github.io/bezierinfo/
 *
 * @param {Number[][]} points Array of [x,y] points: [start, control1, control2, ..., end]
 *   The original segment to split.
 * @param {Number} t Where to split the curve (value between [0, 1])
 * @return {Object} An object { left, right } where left is the segment from 0..t and
 *   right is the segment from t..1.
 */
function decasteljau(points, t) {
    const left = [];
    const right = [];
    function decasteljauRecurse(_points, _t) {
        if (_points.length === 1) {
            left.push(_points[0]);
            right.push(_points[0]);
        }
        else {
            const newPoints = Array(_points.length - 1);
            for (let i = 0; i < newPoints.length; i++) {
                if (i === 0) {
                    left.push(_points[0]);
                }
                if (i === newPoints.length - 1) {
                    right.push(_points[i + 1]);
                }
                newPoints[i] = [
                    (1 - _t) * _points[i][0] + _t * _points[i + 1][0],
                    (1 - _t) * _points[i][1] + _t * _points[i + 1][1],
                ];
            }
            decasteljauRecurse(newPoints, _t);
        }
    }
    if (points.length) {
        decasteljauRecurse(points, t);
    }
    return { left, right: right.reverse() };
}
