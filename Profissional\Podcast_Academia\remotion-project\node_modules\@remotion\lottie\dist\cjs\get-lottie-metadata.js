"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLottieMetadata = void 0;
/**
 * @description Get the basic metadata such as dimensions, duration and framerate of a Lottie animation.
 * @see [Documentation](https://www.remotion.dev/docs/lottie/getlottiemetadata)
 */
const getLottieMetadata = (animationData) => {
    const width = animationData.w;
    const height = animationData.h;
    const framerate = animationData.fr;
    const durationInFrames = animationData.op;
    if (![width, height, framerate, durationInFrames].every(Boolean)) {
        return null;
    }
    return {
        durationInFrames: Math.floor(durationInFrames),
        durationInSeconds: durationInFrames / framerate,
        fps: framerate,
        height,
        width,
    };
};
exports.getLottieMetadata = getLottieMetadata;
