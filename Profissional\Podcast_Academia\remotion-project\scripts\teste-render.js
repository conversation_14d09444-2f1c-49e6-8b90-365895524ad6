#!/usr/bin/env node

const { execSync } = require('child_process');
const path = require('path');

console.log('🎬 TESTE DO SISTEMA REMOTION');
console.log('=============================');

// Caminho para o executável do Remotion
const remotionPath = path.join(__dirname, '..', 'node_modules', '.bin', 'remotion.cmd');

// Propriedades do vídeo
const props = {
  videoPath: "../Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4",
  startTime: 0,
  duration: 15,
  title: "Como construir seu primeiro Agente IA",
  author: "<PERSON>",
  colors: {
    primary: "#FF6B35",
    secondary: "#2E86AB",
    accent: "#A23B72",
    background: "#F18F01",
    text: "#FFFFFF"
  }
};

// Criar diretório de saída
const outputDir = path.join(__dirname, '..', 'output');
const fs = require('fs');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

console.log('🎥 Renderizando Instagram Story de teste...');
console.log('📁 Vídeo:', props.videoPath);
console.log('⏱️  Duração:', props.duration + 's');
console.log('🎨 Cores:', props.colors.primary, props.colors.secondary);

// Comando de renderização
const propsJson = JSON.stringify(props);
const outputFile = path.join(outputDir, 'teste_story.mp4');

try {
  console.log('\n⚡ Executando renderização...');
  
  const command = `"${remotionPath}" render src/index.ts InstagramStory "${outputFile}" --props="${propsJson.replace(/"/g, '\\"')}"`;
  
  console.log('📋 Comando:', command);
  
  const result = execSync(command, { 
    cwd: path.join(__dirname, '..'),
    stdio: 'inherit'
  });
  
  console.log('\n✅ SUCESSO! Vídeo renderizado:');
  console.log('📁', outputFile);
  
  // Verificar se o arquivo foi criado
  if (fs.existsSync(outputFile)) {
    const stats = fs.statSync(outputFile);
    console.log('📊 Tamanho:', (stats.size / 1024 / 1024).toFixed(2) + 'MB');
    console.log('🕐 Criado em:', stats.birthtime);
  }
  
} catch (error) {
  console.error('❌ Erro durante renderização:', error.message);
  
  // Listar arquivos para debug
  console.log('\n🔍 Arquivos disponíveis:');
  console.log('📁 Vídeos:', fs.readdirSync(path.join(__dirname, '..', '..')));
  console.log('📁 Projeto:', fs.readdirSync(path.join(__dirname, '..')));
}

console.log('\n🎉 Teste concluído!');
