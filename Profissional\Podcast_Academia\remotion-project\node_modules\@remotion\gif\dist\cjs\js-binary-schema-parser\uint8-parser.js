"use strict";
// Default stream and parsers for Uint8TypedArray data type
Object.defineProperty(exports, "__esModule", { value: true });
exports.readBits = exports.readArray = exports.readUnsigned = exports.readString = exports.peekBytes = exports.readBytes = exports.peekByte = exports.readByte = exports.buildStream = void 0;
const buildStream = (uint8Data) => ({
    data: uint8Data,
    pos: 0,
});
exports.buildStream = buildStream;
const readByte = () => (stream) => {
    return stream.data[stream.pos++];
};
exports.readByte = readByte;
const peekByte = (offset = 0) => (stream) => {
    return stream.data[stream.pos + offset];
};
exports.peekByte = peekByte;
const readBytes = (length) => (stream) => {
    // eslint-disable-next-line no-return-assign
    return stream.data.subarray(stream.pos, (stream.pos += length));
};
exports.readBytes = readBytes;
const peekBytes = (length) => (stream) => {
    return stream.data.subarray(stream.pos, stream.pos + length);
};
exports.peekBytes = peekBytes;
const readString = (length) => (stream) => {
    return Array.from((0, exports.readBytes)(length)(stream))
        .map((value) => String.fromCharCode(value))
        .join('');
};
exports.readString = readString;
const readUnsigned = (littleEndian) => (stream) => {
    const bytes = (0, exports.readBytes)(2)(stream);
    return littleEndian ? (bytes[1] << 8) + bytes[0] : (bytes[0] << 8) + bytes[1];
};
exports.readUnsigned = readUnsigned;
const readArray = (byteSize, totalOrFunc) => (stream, result, parent) => {
    const total = typeof totalOrFunc === 'function'
        ? totalOrFunc(stream, result, parent)
        : totalOrFunc;
    const parser = (0, exports.readBytes)(byteSize);
    const arr = new Array(total);
    for (let i = 0; i < total; i++) {
        arr[i] = parser(stream);
    }
    return arr;
};
exports.readArray = readArray;
const subBitsTotal = (bits, startIndex, length) => {
    let result = 0;
    for (let i = 0; i < length; i++) {
        result += Number(bits[startIndex + i] && 2 ** (length - i - 1));
    }
    return result;
};
const readBits = (schema) => (stream) => {
    const byte = (0, exports.readByte)()(stream);
    // convert the byte to bit array
    const bits = new Array(8);
    for (let i = 0; i < 8; i++) {
        bits[7 - i] = Boolean(byte & (1 << i));
    }
    // convert the bit array to values based on the schema
    // @ts-expect-error
    return Object.keys(schema).reduce((res, key) => {
        // @ts-expect-error
        const def = schema[key];
        if (def.length) {
            res[key] = subBitsTotal(bits, def.index, def.length);
        }
        else {
            res[key] = bits[def.index];
        }
        return res;
    }, {});
};
exports.readBits = readBits;
