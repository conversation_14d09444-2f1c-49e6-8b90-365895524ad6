export { AvailableOptions, TypeOfOption } from './options';
export { HardwareAccelerationOption } from './options/hardware-acceleration';
export declare const BrowserSafeApis: {
    getFileExtensionFromCodec: <T extends import("./codec").Codec>(codec: T, audioCodec: import("./options/audio-codec").AudioCodec | null) => import("./file-extensions").FileExtension;
    validCodecs: readonly ["h264", "h265", "vp8", "vp9", "mp3", "aac", "wav", "prores", "h264-mkv", "h264-ts", "gif"];
    validAudioCodecs: readonly ["pcm-16", "aac", "mp3", "opus"];
    getDefaultCrfForCodec: (codec: import("./codec").Codec) => number | null;
    getValidCrfRanges: (codec: import("./codec").Codec) => [number, number];
    proResProfileOptions: readonly ["4444-xq", "4444", "hq", "standard", "light", "proxy"];
    x264PresetOptions: readonly ["ultrafast", "superfast", "veryfast", "faster", "fast", "medium", "slow", "slower", "veryslow", "placebo"];
    hardwareAccelerationOptions: readonly ["disable", "if-possible", "required"];
    validPixelFormats: readonly ["yuv420p", "yuva420p", "yuv422p", "yuv444p", "yuv420p10le", "yuv422p10le", "yuv444p10le", "yuva444p10le"];
    validOpenGlRenderers: readonly ["swangle", "angle", "egl", "swiftshader", "vulkan", "angle-egl"];
    validPixelFormatsForCodec: (codec: import("./codec").Codec) => readonly ["yuv420p", "yuva420p", "yuv422p", "yuv444p", "yuv420p10le", "yuv422p10le", "yuv444p10le", "yuva444p10le"] | ("yuv420p" | "yuv422p" | "yuv444p" | "yuv420p10le" | "yuv422p10le" | "yuv444p10le" | "yuva444p10le")[];
    validVideoImageFormats: readonly ["png", "jpeg", "none"];
    validStillImageFormats: readonly ["png", "jpeg", "pdf", "webp"];
    DEFAULT_PIXEL_FORMAT: "yuv420p" | "yuva420p" | "yuv422p" | "yuv444p" | "yuv420p10le" | "yuv422p10le" | "yuv444p10le" | "yuva444p10le";
    DEFAULT_TIMEOUT: number;
    DEFAULT_JPEG_QUALITY: number;
    DEFAULT_COLOR_SPACE: "default";
    supportedAudioCodecs: {
        readonly h264: readonly ["aac", "pcm-16", "mp3"];
        readonly 'h264-mkv': readonly ["pcm-16", "mp3"];
        readonly 'h264-ts': readonly ["pcm-16", "aac"];
        readonly aac: readonly ["aac", "pcm-16"];
        readonly avi: readonly [];
        readonly gif: readonly [];
        readonly h265: readonly ["aac", "pcm-16"];
        readonly mp3: readonly ["mp3", "pcm-16"];
        readonly prores: readonly ["aac", "pcm-16"];
        readonly vp8: readonly ["opus", "pcm-16"];
        readonly vp9: readonly ["opus", "pcm-16"];
        readonly wav: readonly ["pcm-16"];
    };
    defaultFileExtensionMap: {
        h264: {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {
                mp3: {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
                aac: {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
                "pcm-16": {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
            };
        };
        h265: {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {
                aac: {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
                "pcm-16": {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
            };
        };
        vp8: {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {
                "pcm-16": {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
                opus: {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
            };
        };
        vp9: {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {
                "pcm-16": {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
                opus: {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
            };
        };
        mp3: {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {
                mp3: {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
                "pcm-16": {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
            };
        };
        aac: {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {
                aac: {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
                "pcm-16": {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
            };
        };
        wav: {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {
                "pcm-16": {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
            };
        };
        prores: {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {
                aac: {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
                "pcm-16": {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
            };
        };
        "h264-mkv": {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {
                mp3: {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
                "pcm-16": {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
            };
        };
        "h264-ts": {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {
                aac: {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
                "pcm-16": {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
            };
        };
        gif: {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {};
        };
    };
    defaultAudioCodecs: {
        h264: {
            compressed: "mp3" | "aac" | "pcm-16" | null;
            lossless: "mp3" | "aac" | "pcm-16" | null;
        };
        h265: {
            compressed: "aac" | "pcm-16" | null;
            lossless: "aac" | "pcm-16" | null;
        };
        vp8: {
            compressed: "pcm-16" | "opus" | null;
            lossless: "pcm-16" | "opus" | null;
        };
        vp9: {
            compressed: "pcm-16" | "opus" | null;
            lossless: "pcm-16" | "opus" | null;
        };
        mp3: {
            compressed: "mp3" | "pcm-16" | null;
            lossless: "mp3" | "pcm-16" | null;
        };
        aac: {
            compressed: "aac" | "pcm-16" | null;
            lossless: "aac" | "pcm-16" | null;
        };
        wav: {
            compressed: "pcm-16" | null;
            lossless: "pcm-16" | null;
        };
        prores: {
            compressed: "aac" | "pcm-16" | null;
            lossless: "aac" | "pcm-16" | null;
        };
        "h264-mkv": {
            compressed: "mp3" | "pcm-16" | null;
            lossless: "mp3" | "pcm-16" | null;
        };
        "h264-ts": {
            compressed: "aac" | "pcm-16" | null;
            lossless: "aac" | "pcm-16" | null;
        };
        gif: {
            compressed: null;
            lossless: null;
        };
    };
    defaultCodecsForFileExtension: Record<import("./file-extensions").FileExtension, "h264" | "h265" | "vp8" | "vp9" | "mp3" | "aac" | "wav" | "prores" | "h264-mkv" | "h264-ts" | "gif">;
    validateOutputFilename: <T extends import("./codec").Codec>({ codec, audioCodecSetting, extension, preferLossless, separateAudioTo, }: {
        codec: T;
        audioCodecSetting: import("./options/audio-codec").AudioCodec | null;
        extension: string;
        preferLossless: boolean;
        separateAudioTo: string | null;
    }) => void;
    options: {
        audioCodecOption: {
            cliFlag: "audio-codec";
            setConfig: (audioCodec: "mp3" | "aac" | "pcm-16" | "opus" | null) => void;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: import("./options/audio-codec").AudioCodec;
            } | {
                source: string;
                value: null;
            };
            description: () => string;
            docLink: string;
            name: string;
            ssrName: "audioCodec";
            type: import("./options/audio-codec").AudioCodec;
        };
        scaleOption: {
            name: string;
            cliFlag: "scale";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: number;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
            setConfig: (scale: number) => void;
        };
        crfOption: {
            name: string;
            cliFlag: "crf";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: number;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: import("./crf").Crf;
            };
            setConfig: (crf: import("./crf").Crf) => void;
        };
        jpegQualityOption: {
            name: string;
            cliFlag: "jpeg-quality";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: number;
            setConfig: (q: number | undefined) => void;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
        };
        videoBitrateOption: {
            name: string;
            cliFlag: "video-bitrate";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string | null;
            };
            setConfig: (bitrate: string | null) => void;
        };
        audioBitrateOption: {
            name: string;
            cliFlag: "audio-bitrate";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: string;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (value: string | null) => void;
        };
        enforceAudioOption: {
            name: string;
            cliFlag: "enforce-audio-track";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: boolean;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: true;
            } | {
                source: string;
                value: false;
            };
            setConfig: (value: boolean) => void;
        };
        mutedOption: {
            name: string;
            cliFlag: "muted";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: boolean;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: boolean;
            };
            setConfig: () => void;
        };
        videoCodecOption: {
            name: string;
            cliFlag: "codec";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: import("./codec").Codec;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }, { compositionCodec, configFile, downloadName, outName, uiCodec, }: {
                outName: string | null;
                downloadName: string | null;
                configFile: import("./codec").Codec | null;
                uiCodec: import("./codec").Codec | null;
                compositionCodec: import("./codec").Codec | null;
            }) => {
                value: import("./codec").Codec;
                source: string;
            };
            setConfig: (newCodec: import("./codec").CodecOrUndefined) => void;
        };
        offthreadVideoCacheSizeInBytesOption: {
            name: string;
            cliFlag: "offthreadvideo-cache-size-in-bytes";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoCacheSizeInBytes";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        offthreadVideoThreadsOption: {
            name: string;
            cliFlag: "offthreadvideo-video-threads";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoThreads";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        webhookCustomDataOption: {
            name: string;
            cliFlag: "webhook-custom-data";
            description: (type: "cli" | "ssr") => import("react/jsx-runtime").JSX.Element;
            ssrName: "customData";
            docLink: string;
            type: Record<string, unknown> | null;
            getValue: () => never;
            setConfig: () => never;
        };
        colorSpaceOption: {
            name: string;
            cliFlag: "color-space";
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            ssrName: string;
            type: import("./options/color-space").ColorSpace | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: import("./options/color-space").ColorSpace;
            };
            setConfig: (value: "default" | "bt709" | "bt2020-ncl" | null) => void;
        };
        deleteAfterOption: {
            name: string;
            cliFlag: "delete-after";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "deleteAfter";
            docLink: string;
            type: import(".").DeleteAfter | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: import(".").DeleteAfter;
            } | {
                source: string;
                value: null;
            };
            setConfig: (value: import(".").DeleteAfter | null) => void;
        };
        disallowParallelEncodingOption: {
            name: string;
            cliFlag: "disallow-parallel-encoding";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: boolean;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: boolean;
                source: string;
            };
            setConfig(value: boolean): void;
        };
        folderExpiryOption: {
            name: string;
            cliFlag: "enable-folder-expiry";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "enableFolderExpiry";
            docLink: string;
            type: boolean | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: boolean | null;
            };
            setConfig: (value: boolean | null) => void;
        };
        enableMultiprocessOnLinuxOption: {
            name: string;
            cliFlag: "enable-multiprocess-on-linux";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: boolean;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: boolean;
            };
            setConfig: (value: boolean) => void;
        };
        glOption: {
            cliFlag: "gl";
            docLink: string;
            name: string;
            type: import("./options/gl").OpenGlRenderer | null;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./options/gl").OpenGlRenderer;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (value: import("./options/gl").OpenGlRenderer | null) => void;
        };
        enableLambdaInsights: {
            name: string;
            cliFlag: "enable-lambda-insights";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: boolean;
            setConfig: (value: boolean) => void;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: boolean;
                source: string;
            };
        };
        encodingMaxRateOption: {
            name: string;
            cliFlag: "max-rate";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "encodingMaxRate";
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: string;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (newMaxRate: string | null) => void;
        };
        encodingBufferSizeOption: {
            name: string;
            cliFlag: "buffer-size";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "encodingBufferSize";
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: string;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (bitrate: string | null) => void;
        };
        beepOnFinishOption: {
            name: string;
            cliFlag: "beep-on-finish";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: null;
            docLink: string;
            type: boolean;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: boolean;
                source: string;
            };
            setConfig(value: boolean): void;
        };
        numberOfGifLoopsOption: {
            name: string;
            cliFlag: "number-of-gif-loops";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "numberOfGifLoops";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: number;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (newLoop: import(".").NumberOfGifLoops) => void;
        };
        reproOption: {
            name: string;
            cliFlag: "repro";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: boolean;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: boolean;
                source: string;
            };
            setConfig: (should: boolean) => void;
        };
        preferLosslessOption: {
            name: string;
            cliFlag: "prefer-lossless";
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            type: boolean;
            ssrName: "preferLossless";
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: true;
                source: string;
            } | {
                value: false;
                source: string;
            };
            setConfig: (val: boolean) => void;
        };
        x264Option: {
            name: string;
            cliFlag: "x264-preset";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "x264Preset";
            docLink: string;
            type: import("./options/x264-preset").X264Preset | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./options/x264-preset").X264Preset;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (profile: import("./options/x264-preset").X264Preset | null) => void;
        };
        logLevelOption: {
            cliFlag: "log";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./log-level").LogLevel;
                source: string;
            };
            setConfig: (newLogLevel: import("./log-level").LogLevel) => void;
            type: import("./log-level").LogLevel;
        };
        delayRenderTimeoutInMillisecondsOption: {
            name: string;
            cliFlag: "timeout";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "timeoutInMilliseconds";
            docLink: string;
            type: number;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
            setConfig: (value: number) => void;
        };
        headlessOption: {
            name: string;
            cliFlag: "disable-headless";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: boolean;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: boolean;
            };
            setConfig: (value: boolean) => void;
        };
        overwriteOption: {
            name: string;
            cliFlag: "overwrite";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: boolean;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }, defaultValue: boolean) => {
                source: string;
                value: boolean;
            };
            setConfig: (value: boolean) => void;
        };
        binariesDirectoryOption: {
            name: string;
            cliFlag: "binaries-directory";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "binariesDirectory";
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string | null;
            };
            setConfig: (value: string | null) => void;
        };
        forSeamlessAacConcatenationOption: {
            name: string;
            cliFlag: "for-seamless-aac-concatenation";
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: true;
            } | {
                source: string;
                value: false;
            };
            setConfig: (value: boolean) => void;
            ssrName: string;
            type: boolean;
        };
        separateAudioOption: {
            cliFlag: string;
            description: () => string;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string;
            } | {
                source: string;
                value: null;
            };
            name: string;
            setConfig: () => never;
            ssrName: string;
            type: string | null;
        };
        publicPathOption: {
            name: string;
            cliFlag: "public-path";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "publicPath";
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string;
            } | {
                source: string;
                value: null;
            };
            setConfig: (value: string | null) => void;
            type: string | null;
        };
        publicDirOption: {
            name: string;
            cliFlag: "public-dir";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "publicDir";
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string;
            } | {
                source: string;
                value: null;
            };
            setConfig: (value: string | null) => void;
            type: string | null;
        };
        onBrowserDownloadOption: {
            name: string;
            cliFlag: "on-browser-download";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "onBrowserDownload";
            docLink: string;
            type: import(".").OnBrowserDownload;
            getValue: () => never;
            setConfig: () => never;
        };
        throwIfSiteExistsOption: {
            cliFlag: string;
            description: () => string;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: boolean;
            };
            name: string;
            setConfig: () => never;
            ssrName: string;
            type: boolean;
        };
        disableGitSourceOption: {
            cliFlag: string;
            description: () => string;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: boolean;
            };
            name: string;
            setConfig: () => never;
            ssrName: string;
            type: boolean;
        };
        metadataOption: {
            name: string;
            cliFlag: "metadata";
            description: (mode: "cli" | "ssr") => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            type: import("./options/metadata").Metadata;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: {
                    [k: string]: string;
                };
            };
            setConfig: (newMetadata: import("./options/metadata").Metadata) => void;
            ssrName: string;
        };
        hardwareAccelerationOption: {
            name: string;
            cliFlag: "hardware-acceleration";
            description: () => string;
            ssrName: string;
            docLink: string;
            type: import("./options/hardware-acceleration").HardwareAccelerationOption;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: "disable" | "if-possible" | "required";
            };
            setConfig: (value: import("./options/hardware-acceleration").HardwareAccelerationOption) => void;
        };
        chromeModeOption: {
            cliFlag: "chrome-mode";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./options/chrome-mode").ChromeMode;
                source: string;
            };
            setConfig: (newChromeMode: import("./options/chrome-mode").ChromeMode) => void;
            type: import("./options/chrome-mode").ChromeMode;
        };
        apiKeyOption: {
            name: string;
            cliFlag: "api-key";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "apiKey";
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string | null;
            };
            setConfig: (value: string | null) => void;
        };
        audioLatencyHintOption: {
            name: string;
            cliFlag: "audio-latency-hint";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "audioLatencyHint";
            docLink: string;
            type: AudioContextLatencyCategory;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: AudioContextLatencyCategory;
                source: string;
            } | {
                value: null;
                source: string;
            };
            setConfig: (profile: AudioContextLatencyCategory | null) => void;
        };
        enableCrossSiteIsolationOption: {
            name: string;
            cliFlag: "cross-site-isolation";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: null;
            docLink: string;
            type: boolean;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: boolean;
                source: string;
            };
            setConfig(value: boolean): void;
        };
        imageSequencePatternOption: {
            name: string;
            cliFlag: "image-sequence-pattern";
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: null;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: string;
                source: string;
            };
            setConfig: (pattern: string | null) => void;
        };
    };
    validColorSpaces: readonly ["default", "bt709", "bt2020-ncl"];
    optionsMap: {
        readonly renderMedia: {
            readonly offthreadVideoCacheSizeInBytes: {
                name: string;
                cliFlag: "offthreadvideo-cache-size-in-bytes";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "offthreadVideoCacheSizeInBytes";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (size: number | null) => void;
            };
            readonly offthreadVideoThreads: {
                name: string;
                cliFlag: "offthreadvideo-video-threads";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "offthreadVideoThreads";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (size: number | null) => void;
            };
            readonly videoBitrate: {
                name: string;
                cliFlag: "video-bitrate";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: string | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: string | null;
                };
                setConfig: (bitrate: string | null) => void;
            };
            readonly numberOfGifLoops: {
                name: string;
                cliFlag: "number-of-gif-loops";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "numberOfGifLoops";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: number;
                    source: string;
                } | {
                    value: null;
                    source: string;
                };
                setConfig: (newLoop: import(".").NumberOfGifLoops) => void;
            };
            readonly repro: {
                name: string;
                cliFlag: "repro";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: boolean;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: boolean;
                    source: string;
                };
                setConfig: (should: boolean) => void;
            };
            readonly x264Preset: {
                name: string;
                cliFlag: "x264-preset";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "x264Preset";
                docLink: string;
                type: import("./options/x264-preset").X264Preset | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./options/x264-preset").X264Preset;
                    source: string;
                } | {
                    value: null;
                    source: string;
                };
                setConfig: (profile: import("./options/x264-preset").X264Preset | null) => void;
            };
            readonly audioBitrate: {
                name: string;
                cliFlag: "audio-bitrate";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: string;
                    source: string;
                } | {
                    value: null;
                    source: string;
                };
                setConfig: (value: string | null) => void;
            };
            readonly colorSpace: {
                name: string;
                cliFlag: "color-space";
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                ssrName: string;
                type: import("./options/color-space").ColorSpace | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: import("./options/color-space").ColorSpace;
                };
                setConfig: (value: "default" | "bt709" | "bt2020-ncl" | null) => void;
            };
            readonly codec: {
                name: string;
                cliFlag: "codec";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: import("./codec").Codec;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }, { compositionCodec, configFile, downloadName, outName, uiCodec, }: {
                    outName: string | null;
                    downloadName: string | null;
                    configFile: import("./codec").Codec | null;
                    uiCodec: import("./codec").Codec | null;
                    compositionCodec: import("./codec").Codec | null;
                }) => {
                    value: import("./codec").Codec;
                    source: string;
                };
                setConfig: (newCodec: import("./codec").CodecOrUndefined) => void;
            };
            readonly disallowParallelEncoding: {
                name: string;
                cliFlag: "disallow-parallel-encoding";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: boolean;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: boolean;
                    source: string;
                };
                setConfig(value: boolean): void;
            };
            readonly jpegQuality: {
                name: string;
                cliFlag: "jpeg-quality";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: number;
                setConfig: (q: number | undefined) => void;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                };
            };
            readonly encodingMaxRate: {
                name: string;
                cliFlag: "max-rate";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "encodingMaxRate";
                docLink: string;
                type: string | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: string;
                    source: string;
                } | {
                    value: null;
                    source: string;
                };
                setConfig: (newMaxRate: string | null) => void;
            };
            readonly encodingBufferSize: {
                name: string;
                cliFlag: "buffer-size";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "encodingBufferSize";
                docLink: string;
                type: string | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: string;
                    source: string;
                } | {
                    value: null;
                    source: string;
                };
                setConfig: (bitrate: string | null) => void;
            };
            readonly muted: {
                name: string;
                cliFlag: "muted";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: boolean;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: boolean;
                };
                setConfig: () => void;
            };
            readonly logLevel: {
                cliFlag: "log";
                name: string;
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./log-level").LogLevel;
                    source: string;
                };
                setConfig: (newLogLevel: import("./log-level").LogLevel) => void;
                type: import("./log-level").LogLevel;
            };
            readonly timeoutInMilliseconds: {
                name: string;
                cliFlag: "timeout";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "timeoutInMilliseconds";
                docLink: string;
                type: number;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                };
                setConfig: (value: number) => void;
            };
            readonly binariesDirectory: {
                name: string;
                cliFlag: "binaries-directory";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "binariesDirectory";
                docLink: string;
                type: string | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: string | null;
                };
                setConfig: (value: string | null) => void;
            };
            readonly forSeamlessAacConcatenation: {
                name: string;
                cliFlag: "for-seamless-aac-concatenation";
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: true;
                } | {
                    source: string;
                    value: false;
                };
                setConfig: (value: boolean) => void;
                ssrName: string;
                type: boolean;
            };
            readonly separateAudioTo: {
                cliFlag: string;
                description: () => string;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: string;
                } | {
                    source: string;
                    value: null;
                };
                name: string;
                setConfig: () => never;
                ssrName: string;
                type: string | null;
            };
            readonly audioCodec: {
                cliFlag: "audio-codec";
                setConfig: (audioCodec: "mp3" | "aac" | "pcm-16" | "opus" | null) => void;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: import("./options/audio-codec").AudioCodec;
                } | {
                    source: string;
                    value: null;
                };
                description: () => string;
                docLink: string;
                name: string;
                ssrName: "audioCodec";
                type: import("./options/audio-codec").AudioCodec;
            };
            readonly onBrowserDownload: {
                name: string;
                cliFlag: "on-browser-download";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "onBrowserDownload";
                docLink: string;
                type: import(".").OnBrowserDownload;
                getValue: () => never;
                setConfig: () => never;
            };
            readonly hardwareAcceleration: {
                name: string;
                cliFlag: "hardware-acceleration";
                description: () => string;
                ssrName: string;
                docLink: string;
                type: import("./options/hardware-acceleration").HardwareAccelerationOption;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: "disable" | "if-possible" | "required";
                };
                setConfig: (value: import("./options/hardware-acceleration").HardwareAccelerationOption) => void;
            };
            readonly chromeMode: {
                cliFlag: "chrome-mode";
                name: string;
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./options/chrome-mode").ChromeMode;
                    source: string;
                };
                setConfig: (newChromeMode: import("./options/chrome-mode").ChromeMode) => void;
                type: import("./options/chrome-mode").ChromeMode;
            };
        };
        readonly stitchFramesToVideo: {
            readonly separateAudioTo: {
                cliFlag: string;
                description: () => string;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: string;
                } | {
                    source: string;
                    value: null;
                };
                name: string;
                setConfig: () => never;
                ssrName: string;
                type: string | null;
            };
            readonly hardwareAcceleration: {
                name: string;
                cliFlag: "hardware-acceleration";
                description: () => string;
                ssrName: string;
                docLink: string;
                type: import("./options/hardware-acceleration").HardwareAccelerationOption;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: "disable" | "if-possible" | "required";
                };
                setConfig: (value: import("./options/hardware-acceleration").HardwareAccelerationOption) => void;
            };
        };
        readonly renderStill: {
            readonly offthreadVideoCacheSizeInBytes: {
                name: string;
                cliFlag: "offthreadvideo-cache-size-in-bytes";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "offthreadVideoCacheSizeInBytes";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (size: number | null) => void;
            };
            readonly offthreadVideoThreads: {
                name: string;
                cliFlag: "offthreadvideo-video-threads";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "offthreadVideoThreads";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (size: number | null) => void;
            };
            readonly jpegQuality: {
                name: string;
                cliFlag: "jpeg-quality";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: number;
                setConfig: (q: number | undefined) => void;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                };
            };
            readonly logLevel: {
                cliFlag: "log";
                name: string;
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./log-level").LogLevel;
                    source: string;
                };
                setConfig: (newLogLevel: import("./log-level").LogLevel) => void;
                type: import("./log-level").LogLevel;
            };
            readonly timeoutInMilliseconds: {
                name: string;
                cliFlag: "timeout";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "timeoutInMilliseconds";
                docLink: string;
                type: number;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                };
                setConfig: (value: number) => void;
            };
            readonly binariesDirectory: {
                name: string;
                cliFlag: "binaries-directory";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "binariesDirectory";
                docLink: string;
                type: string | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: string | null;
                };
                setConfig: (value: string | null) => void;
            };
            readonly onBrowserDownload: {
                name: string;
                cliFlag: "on-browser-download";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "onBrowserDownload";
                docLink: string;
                type: import(".").OnBrowserDownload;
                getValue: () => never;
                setConfig: () => never;
            };
            readonly chromeMode: {
                cliFlag: "chrome-mode";
                name: string;
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./options/chrome-mode").ChromeMode;
                    source: string;
                };
                setConfig: (newChromeMode: import("./options/chrome-mode").ChromeMode) => void;
                type: import("./options/chrome-mode").ChromeMode;
            };
        };
        readonly getCompositions: {
            readonly offthreadVideoCacheSizeInBytes: {
                name: string;
                cliFlag: "offthreadvideo-cache-size-in-bytes";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "offthreadVideoCacheSizeInBytes";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (size: number | null) => void;
            };
            readonly offthreadVideoThreads: {
                name: string;
                cliFlag: "offthreadvideo-video-threads";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "offthreadVideoThreads";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (size: number | null) => void;
            };
            readonly logLevel: {
                cliFlag: "log";
                name: string;
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./log-level").LogLevel;
                    source: string;
                };
                setConfig: (newLogLevel: import("./log-level").LogLevel) => void;
                type: import("./log-level").LogLevel;
            };
            readonly timeoutInMilliseconds: {
                name: string;
                cliFlag: "timeout";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "timeoutInMilliseconds";
                docLink: string;
                type: number;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                };
                setConfig: (value: number) => void;
            };
            readonly binariesDirectory: {
                name: string;
                cliFlag: "binaries-directory";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "binariesDirectory";
                docLink: string;
                type: string | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: string | null;
                };
                setConfig: (value: string | null) => void;
            };
            readonly onBrowserDownload: {
                name: string;
                cliFlag: "on-browser-download";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "onBrowserDownload";
                docLink: string;
                type: import(".").OnBrowserDownload;
                getValue: () => never;
                setConfig: () => never;
            };
            readonly chromeMode: {
                cliFlag: "chrome-mode";
                name: string;
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./options/chrome-mode").ChromeMode;
                    source: string;
                };
                setConfig: (newChromeMode: import("./options/chrome-mode").ChromeMode) => void;
                type: import("./options/chrome-mode").ChromeMode;
            };
        };
        readonly selectComposition: {
            readonly offthreadVideoCacheSizeInBytes: {
                name: string;
                cliFlag: "offthreadvideo-cache-size-in-bytes";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "offthreadVideoCacheSizeInBytes";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (size: number | null) => void;
            };
            readonly offthreadVideoThreads: {
                name: string;
                cliFlag: "offthreadvideo-video-threads";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "offthreadVideoThreads";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (size: number | null) => void;
            };
            readonly logLevel: {
                cliFlag: "log";
                name: string;
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./log-level").LogLevel;
                    source: string;
                };
                setConfig: (newLogLevel: import("./log-level").LogLevel) => void;
                type: import("./log-level").LogLevel;
            };
            readonly timeoutInMilliseconds: {
                name: string;
                cliFlag: "timeout";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "timeoutInMilliseconds";
                docLink: string;
                type: number;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                };
                setConfig: (value: number) => void;
            };
            readonly binariesDirectory: {
                name: string;
                cliFlag: "binaries-directory";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "binariesDirectory";
                docLink: string;
                type: string | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: string | null;
                };
                setConfig: (value: string | null) => void;
            };
            readonly onBrowserDownload: {
                name: string;
                cliFlag: "on-browser-download";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "onBrowserDownload";
                docLink: string;
                type: import(".").OnBrowserDownload;
                getValue: () => never;
                setConfig: () => never;
            };
            readonly chromeMode: {
                cliFlag: "chrome-mode";
                name: string;
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./options/chrome-mode").ChromeMode;
                    source: string;
                };
                setConfig: (newChromeMode: import("./options/chrome-mode").ChromeMode) => void;
                type: import("./options/chrome-mode").ChromeMode;
            };
        };
        readonly renderFrames: {
            readonly forSeamlessAacConcatenation: {
                name: string;
                cliFlag: "for-seamless-aac-concatenation";
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: true;
                } | {
                    source: string;
                    value: false;
                };
                setConfig: (value: boolean) => void;
                ssrName: string;
                type: boolean;
            };
            readonly offthreadVideoCacheSizeInBytes: {
                name: string;
                cliFlag: "offthreadvideo-cache-size-in-bytes";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "offthreadVideoCacheSizeInBytes";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (size: number | null) => void;
            };
            readonly offthreadVideoThreads: {
                name: string;
                cliFlag: "offthreadvideo-video-threads";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "offthreadVideoThreads";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (size: number | null) => void;
            };
            readonly jpegQuality: {
                name: string;
                cliFlag: "jpeg-quality";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: number;
                setConfig: (q: number | undefined) => void;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                };
            };
            readonly logLevel: {
                cliFlag: "log";
                name: string;
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./log-level").LogLevel;
                    source: string;
                };
                setConfig: (newLogLevel: import("./log-level").LogLevel) => void;
                type: import("./log-level").LogLevel;
            };
            readonly timeoutInMilliseconds: {
                name: string;
                cliFlag: "timeout";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "timeoutInMilliseconds";
                docLink: string;
                type: number;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                };
                setConfig: (value: number) => void;
            };
            readonly binariesDirectory: {
                name: string;
                cliFlag: "binaries-directory";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "binariesDirectory";
                docLink: string;
                type: string | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: string | null;
                };
                setConfig: (value: string | null) => void;
            };
            readonly onBrowserDownload: {
                name: string;
                cliFlag: "on-browser-download";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "onBrowserDownload";
                docLink: string;
                type: import(".").OnBrowserDownload;
                getValue: () => never;
                setConfig: () => never;
            };
            readonly chromeMode: {
                cliFlag: "chrome-mode";
                name: string;
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./options/chrome-mode").ChromeMode;
                    source: string;
                };
                setConfig: (newChromeMode: import("./options/chrome-mode").ChromeMode) => void;
                type: import("./options/chrome-mode").ChromeMode;
            };
            readonly imageSequencePattern: {
                name: string;
                cliFlag: "image-sequence-pattern";
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: null;
                type: string | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: string;
                    source: string;
                };
                setConfig: (pattern: string | null) => void;
            };
        };
        readonly renderMediaOnLambda: {
            readonly offthreadVideoCacheSizeInBytes: {
                name: string;
                cliFlag: "offthreadvideo-cache-size-in-bytes";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "offthreadVideoCacheSizeInBytes";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (size: number | null) => void;
            };
            readonly offthreadVideoThreads: {
                name: string;
                cliFlag: "offthreadvideo-video-threads";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "offthreadVideoThreads";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (size: number | null) => void;
            };
            readonly videoBitrate: {
                name: string;
                cliFlag: "video-bitrate";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: string | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: string | null;
                };
                setConfig: (bitrate: string | null) => void;
            };
            readonly numberOfGifLoops: {
                name: string;
                cliFlag: "number-of-gif-loops";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "numberOfGifLoops";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: number;
                    source: string;
                } | {
                    value: null;
                    source: string;
                };
                setConfig: (newLoop: import(".").NumberOfGifLoops) => void;
            };
            readonly preferLossless: {
                name: string;
                cliFlag: "prefer-lossless";
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                type: boolean;
                ssrName: "preferLossless";
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: true;
                    source: string;
                } | {
                    value: false;
                    source: string;
                };
                setConfig: (val: boolean) => void;
            };
            readonly audioBitrate: {
                name: string;
                cliFlag: "audio-bitrate";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: string;
                    source: string;
                } | {
                    value: null;
                    source: string;
                };
                setConfig: (value: string | null) => void;
            };
            readonly deleteAfter: {
                name: string;
                cliFlag: "delete-after";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "deleteAfter";
                docLink: string;
                type: import(".").DeleteAfter | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: import(".").DeleteAfter;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (value: import(".").DeleteAfter | null) => void;
            };
            readonly x264Preset: {
                name: string;
                cliFlag: "x264-preset";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "x264Preset";
                docLink: string;
                type: import("./options/x264-preset").X264Preset | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./options/x264-preset").X264Preset;
                    source: string;
                } | {
                    value: null;
                    source: string;
                };
                setConfig: (profile: import("./options/x264-preset").X264Preset | null) => void;
            };
            readonly encodingMaxRate: {
                name: string;
                cliFlag: "max-rate";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "encodingMaxRate";
                docLink: string;
                type: string | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: string;
                    source: string;
                } | {
                    value: null;
                    source: string;
                };
                setConfig: (newMaxRate: string | null) => void;
            };
            readonly encodingBufferSize: {
                name: string;
                cliFlag: "buffer-size";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "encodingBufferSize";
                docLink: string;
                type: string | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: string;
                    source: string;
                } | {
                    value: null;
                    source: string;
                };
                setConfig: (bitrate: string | null) => void;
            };
            readonly colorSpace: {
                name: string;
                cliFlag: "color-space";
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                ssrName: string;
                type: import("./options/color-space").ColorSpace | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: import("./options/color-space").ColorSpace;
                };
                setConfig: (value: "default" | "bt709" | "bt2020-ncl" | null) => void;
            };
            readonly muted: {
                name: string;
                cliFlag: "muted";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: boolean;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: boolean;
                };
                setConfig: () => void;
            };
            readonly logLevel: {
                cliFlag: "log";
                name: string;
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./log-level").LogLevel;
                    source: string;
                };
                setConfig: (newLogLevel: import("./log-level").LogLevel) => void;
                type: import("./log-level").LogLevel;
            };
            readonly timeoutInMilliseconds: {
                name: string;
                cliFlag: "timeout";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "timeoutInMilliseconds";
                docLink: string;
                type: number;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                };
                setConfig: (value: number) => void;
            };
            readonly apiKey: {
                name: string;
                cliFlag: "api-key";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "apiKey";
                docLink: string;
                type: string | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: string | null;
                };
                setConfig: (value: string | null) => void;
            };
        };
        readonly renderStillOnLambda: {
            readonly offthreadVideoCacheSizeInBytes: {
                name: string;
                cliFlag: "offthreadvideo-cache-size-in-bytes";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "offthreadVideoCacheSizeInBytes";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (size: number | null) => void;
            };
            readonly offthreadVideoThreads: {
                name: string;
                cliFlag: "offthreadvideo-video-threads";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "offthreadVideoThreads";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (size: number | null) => void;
            };
            readonly jpegQuality: {
                name: string;
                cliFlag: "jpeg-quality";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: number;
                setConfig: (q: number | undefined) => void;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                };
            };
            readonly logLevel: {
                cliFlag: "log";
                name: string;
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./log-level").LogLevel;
                    source: string;
                };
                setConfig: (newLogLevel: import("./log-level").LogLevel) => void;
                type: import("./log-level").LogLevel;
            };
            readonly deleteAfter: {
                name: string;
                cliFlag: "delete-after";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "deleteAfter";
                docLink: string;
                type: import(".").DeleteAfter | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: import(".").DeleteAfter;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (value: import(".").DeleteAfter | null) => void;
            };
            readonly scale: {
                name: string;
                cliFlag: "scale";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: number;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                };
                setConfig: (scale: number) => void;
            };
            readonly timeoutInMilliseconds: {
                name: string;
                cliFlag: "timeout";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "timeoutInMilliseconds";
                docLink: string;
                type: number;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                };
                setConfig: (value: number) => void;
            };
            readonly apiKey: {
                name: string;
                cliFlag: "api-key";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "apiKey";
                docLink: string;
                type: string | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: string | null;
                };
                setConfig: (value: string | null) => void;
            };
        };
        readonly getCompositionsOnLambda: {
            readonly offthreadVideoCacheSizeInBytes: {
                name: string;
                cliFlag: "offthreadvideo-cache-size-in-bytes";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "offthreadVideoCacheSizeInBytes";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (size: number | null) => void;
            };
            readonly offthreadVideoThreads: {
                name: string;
                cliFlag: "offthreadvideo-video-threads";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "offthreadVideoThreads";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (size: number | null) => void;
            };
            readonly logLevel: {
                cliFlag: "log";
                name: string;
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./log-level").LogLevel;
                    source: string;
                };
                setConfig: (newLogLevel: import("./log-level").LogLevel) => void;
                type: import("./log-level").LogLevel;
            };
            readonly timeoutInMilliseconds: {
                name: string;
                cliFlag: "timeout";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "timeoutInMilliseconds";
                docLink: string;
                type: number;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                };
                setConfig: (value: number) => void;
            };
        };
        readonly renderMediaOnCloudRun: {
            readonly offthreadVideoCacheSizeInBytes: {
                name: string;
                cliFlag: "offthreadvideo-cache-size-in-bytes";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "offthreadVideoCacheSizeInBytes";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (size: number | null) => void;
            };
            readonly offthreadVideoThreads: {
                name: string;
                cliFlag: "offthreadvideo-video-threads";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "offthreadVideoThreads";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (size: number | null) => void;
            };
            readonly numberOfGifLoops: {
                name: string;
                cliFlag: "number-of-gif-loops";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "numberOfGifLoops";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: number;
                    source: string;
                } | {
                    value: null;
                    source: string;
                };
                setConfig: (newLoop: import(".").NumberOfGifLoops) => void;
            };
            readonly preferLossless: {
                name: string;
                cliFlag: "prefer-lossless";
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                type: boolean;
                ssrName: "preferLossless";
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: true;
                    source: string;
                } | {
                    value: false;
                    source: string;
                };
                setConfig: (val: boolean) => void;
            };
            readonly colorSpace: {
                name: string;
                cliFlag: "color-space";
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                ssrName: string;
                type: import("./options/color-space").ColorSpace | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: import("./options/color-space").ColorSpace;
                };
                setConfig: (value: "default" | "bt709" | "bt2020-ncl" | null) => void;
            };
            readonly audioBitrate: {
                name: string;
                cliFlag: "audio-bitrate";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: string;
                    source: string;
                } | {
                    value: null;
                    source: string;
                };
                setConfig: (value: string | null) => void;
            };
            readonly videoBitrate: {
                name: string;
                cliFlag: "video-bitrate";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: string | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: string | null;
                };
                setConfig: (bitrate: string | null) => void;
            };
            readonly x264Preset: {
                name: string;
                cliFlag: "x264-preset";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "x264Preset";
                docLink: string;
                type: import("./options/x264-preset").X264Preset | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./options/x264-preset").X264Preset;
                    source: string;
                } | {
                    value: null;
                    source: string;
                };
                setConfig: (profile: import("./options/x264-preset").X264Preset | null) => void;
            };
            readonly encodingMaxRate: {
                name: string;
                cliFlag: "max-rate";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "encodingMaxRate";
                docLink: string;
                type: string | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: string;
                    source: string;
                } | {
                    value: null;
                    source: string;
                };
                setConfig: (newMaxRate: string | null) => void;
            };
            readonly encodingBufferSize: {
                name: string;
                cliFlag: "buffer-size";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "encodingBufferSize";
                docLink: string;
                type: string | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: string;
                    source: string;
                } | {
                    value: null;
                    source: string;
                };
                setConfig: (bitrate: string | null) => void;
            };
            readonly muted: {
                name: string;
                cliFlag: "muted";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: boolean;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: boolean;
                };
                setConfig: () => void;
            };
            readonly logLevel: {
                cliFlag: "log";
                name: string;
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./log-level").LogLevel;
                    source: string;
                };
                setConfig: (newLogLevel: import("./log-level").LogLevel) => void;
                type: import("./log-level").LogLevel;
            };
            readonly delayRenderTimeoutInMilliseconds: {
                name: string;
                cliFlag: "timeout";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "timeoutInMilliseconds";
                docLink: string;
                type: number;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                };
                setConfig: (value: number) => void;
            };
            readonly enforceAudioTrack: {
                name: string;
                cliFlag: "enforce-audio-track";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: boolean;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: true;
                } | {
                    source: string;
                    value: false;
                };
                setConfig: (value: boolean) => void;
            };
            readonly scale: {
                name: string;
                cliFlag: "scale";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: number;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                };
                setConfig: (scale: number) => void;
            };
            readonly crf: {
                name: string;
                cliFlag: "crf";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: number;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: import("./crf").Crf;
                };
                setConfig: (crf: import("./crf").Crf) => void;
            };
            readonly jpegQuality: {
                name: string;
                cliFlag: "jpeg-quality";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: number;
                setConfig: (q: number | undefined) => void;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                };
            };
        };
        readonly renderStillOnCloudRun: {
            readonly offthreadVideoCacheSizeInBytes: {
                name: string;
                cliFlag: "offthreadvideo-cache-size-in-bytes";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "offthreadVideoCacheSizeInBytes";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (size: number | null) => void;
            };
            readonly offthreadVideoThreads: {
                name: string;
                cliFlag: "offthreadvideo-video-threads";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "offthreadVideoThreads";
                docLink: string;
                type: number | null;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                } | {
                    source: string;
                    value: null;
                };
                setConfig: (size: number | null) => void;
            };
            readonly logLevel: {
                cliFlag: "log";
                name: string;
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./log-level").LogLevel;
                    source: string;
                };
                setConfig: (newLogLevel: import("./log-level").LogLevel) => void;
                type: import("./log-level").LogLevel;
            };
            readonly scale: {
                name: string;
                cliFlag: "scale";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: number;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                };
                setConfig: (scale: number) => void;
            };
            readonly jpegQuality: {
                name: string;
                cliFlag: "jpeg-quality";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: string;
                docLink: string;
                type: number;
                setConfig: (q: number | undefined) => void;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                };
            };
            readonly delayRenderTimeoutInMilliseconds: {
                name: string;
                cliFlag: "timeout";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "timeoutInMilliseconds";
                docLink: string;
                type: number;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: number;
                };
                setConfig: (value: number) => void;
            };
        };
        readonly ensureBrowser: {
            readonly logLevel: {
                cliFlag: "log";
                name: string;
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./log-level").LogLevel;
                    source: string;
                };
                setConfig: (newLogLevel: import("./log-level").LogLevel) => void;
                type: import("./log-level").LogLevel;
            };
            readonly onBrowserDownload: {
                name: string;
                cliFlag: "on-browser-download";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "onBrowserDownload";
                docLink: string;
                type: import(".").OnBrowserDownload;
                getValue: () => never;
                setConfig: () => never;
            };
            readonly chromeMode: {
                cliFlag: "chrome-mode";
                name: string;
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./options/chrome-mode").ChromeMode;
                    source: string;
                };
                setConfig: (newChromeMode: import("./options/chrome-mode").ChromeMode) => void;
                type: import("./options/chrome-mode").ChromeMode;
            };
        };
        readonly openBrowser: {
            readonly logLevel: {
                cliFlag: "log";
                name: string;
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./log-level").LogLevel;
                    source: string;
                };
                setConfig: (newLogLevel: import("./log-level").LogLevel) => void;
                type: import("./log-level").LogLevel;
            };
            readonly onBrowserDownload: {
                name: string;
                cliFlag: "on-browser-download";
                description: () => import("react/jsx-runtime").JSX.Element;
                ssrName: "onBrowserDownload";
                docLink: string;
                type: import(".").OnBrowserDownload;
                getValue: () => never;
                setConfig: () => never;
            };
            readonly chromeMode: {
                cliFlag: "chrome-mode";
                name: string;
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./options/chrome-mode").ChromeMode;
                    source: string;
                };
                setConfig: (newChromeMode: import("./options/chrome-mode").ChromeMode) => void;
                type: import("./options/chrome-mode").ChromeMode;
            };
        };
        readonly deploySiteLambda: {
            readonly logLevel: {
                cliFlag: "log";
                name: string;
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./log-level").LogLevel;
                    source: string;
                };
                setConfig: (newLogLevel: import("./log-level").LogLevel) => void;
                type: import("./log-level").LogLevel;
            };
            readonly throwIfSiteExists: {
                cliFlag: string;
                description: () => string;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    source: string;
                    value: boolean;
                };
                name: string;
                setConfig: () => never;
                ssrName: string;
                type: boolean;
            };
        };
        readonly deploySiteCloudRun: {
            readonly logLevel: {
                cliFlag: "log";
                name: string;
                ssrName: string;
                description: () => import("react/jsx-runtime").JSX.Element;
                docLink: string;
                getValue: ({ commandLine }: {
                    commandLine: Record<string, unknown>;
                }) => {
                    value: import("./log-level").LogLevel;
                    source: string;
                };
                setConfig: (newLogLevel: import("./log-level").LogLevel) => void;
                type: import("./log-level").LogLevel;
            };
        };
    };
    codecSupportsCrf: (codec: import("./codec").Codec) => boolean;
    codecSupportsVideoBitrate: (codec: import("./codec").Codec) => boolean;
    logLevels: readonly ["trace", "verbose", "info", "warn", "error"];
    getOutputCodecOrUndefined: () => import("./codec").CodecOrUndefined;
    getExtensionFromAudioCodec: (audioCodec: import("./options/audio-codec").AudioCodec) => "mp3" | "aac" | "wav" | "opus";
    validChromeModeOptions: readonly ["headless-shell", "chrome-for-testing"];
};
