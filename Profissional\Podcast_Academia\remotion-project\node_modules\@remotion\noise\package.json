{"repository": {"url": "https://github.com/remotion-dev/remotion/tree/main/packages/noise"}, "name": "@remotion/noise", "version": "4.0.324", "description": "Noise generation functions", "main": "dist/cjs/index.js", "types": "dist/cjs/index.d.ts", "module": "dist/esm/index.mjs", "sideEffects": false, "exports": {"./package.json": "./package.json", ".": {"types": "./dist/cjs/index.d.ts", "module": "./dist/esm/index.mjs", "import": "./dist/esm/index.mjs", "require": "./dist/cjs/index.js"}}, "author": "<PERSON><PERSON> (https://github.com/satelllte)", "maintainers": ["<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> (https://github.com/satelllte)"], "contributors": [], "license": "MIT", "bugs": {"url": "https://github.com/remotion-dev/remotion/issues"}, "dependencies": {"simplex-noise": "4.0.1", "remotion": "4.0.324"}, "devDependencies": {"eslint": "9.19.0", "@remotion/eslint-config-internal": "4.0.324"}, "keywords": ["remotion", "noise"], "publishConfig": {"access": "public"}, "homepage": "https://www.remotion.dev/docs/noise", "scripts": {"formatting": "prettier --experimental-cli src --check", "lint": "eslint src", "make": "tsc -d && bun --env-file=../.env.bundle bundle.ts", "test": "bun test src"}}