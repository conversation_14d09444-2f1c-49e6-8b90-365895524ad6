{"repository": {"url": "https://github.com/remotion-dev/remotion/tree/main/packages/shapes"}, "name": "@remotion/shapes", "version": "4.0.324", "description": "Generate SVG shapes", "main": "dist/index.js", "sideEffects": false, "types": "dist/index.d.ts", "module": "dist/esm/index.mjs", "author": "<PERSON>, <PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/remotion-dev/remotion/issues"}, "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "module": "./dist/esm/index.mjs", "import": "./dist/esm/index.mjs", "require": "./dist/index.js"}}, "devDependencies": {"@testing-library/react": "16.1.0", "react": "19.0.0", "react-dom": "19.0.0", "@happy-dom/global-registrator": "14.5.1", "eslint": "9.19.0", "@remotion/eslint-config-internal": "4.0.324"}, "keywords": ["svg", "path", "utilities"], "publishConfig": {"access": "public"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dependencies": {"@remotion/paths": "4.0.324"}, "homepage": "https://www.remotion.dev/docs/shapes", "scripts": {"formatting": "prettier --experimental-cli src --check", "lint": "eslint src", "test": "bun test src", "make": "tsc -d && bun --env-file=../.env.bundle bundle.ts"}}