import type { Instruction } from '@remotion/paths';
import React from 'react';
export type AllShapesProps = Omit<React.SVGProps<SVGPathElement>, 'width' | 'height' | 'd'> & {
    readonly debug?: boolean;
    readonly pathStyle?: React.CSSProperties;
};
export declare const RenderSvg: ({ width, height, path, style, pathStyle, transformOrigin, debug, instructions, ...props }: {
    readonly width: number;
    readonly height: number;
    readonly path: string;
    readonly instructions: Instruction[];
    readonly transformOrigin: string;
} & AllShapesProps) => import("react/jsx-runtime").JSX.Element;
