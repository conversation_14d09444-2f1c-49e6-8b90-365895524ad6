"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.conditional = exports.loop = exports.parse = void 0;
const parse = (stream, schema, result = {}, parent = result) => {
    if (Array.isArray(schema)) {
        schema.forEach((partSchema) => {
            return (0, exports.parse)(stream, partSchema, result, parent);
        });
    }
    else if (typeof schema === 'function') {
        schema(stream, result, parent, exports.parse);
    }
    else {
        // @ts-expect-error
        const key = Object.keys(schema)[0];
        // @ts-expect-error
        if (Array.isArray(schema[key])) {
            // @ts-expect-error
            parent[key] = {};
            // @ts-expect-error
            (0, exports.parse)(stream, schema[key], result, parent[key]);
        }
        else {
            // @ts-expect-error
            parent[key] = schema[key](stream, result, parent, exports.parse);
        }
    }
    return result;
};
exports.parse = parse;
const loop = (schema, continueFunc) => {
    return function (stream, result, parent, _parse) {
        const arr = [];
        let lastStreamPos = stream.pos;
        while (continueFunc(stream, result, parent)) {
            const newParent = {};
            _parse(stream, schema, result, newParent); // cases when whole file is parsed but no termination is there and stream position is not getting updated as well
            // it falls into infinite recursion, null check to avoid the same
            if (stream.pos === lastStreamPos) {
                break;
            }
            lastStreamPos = stream.pos;
            arr.push(newParent);
        }
        return arr;
    };
};
exports.loop = loop;
const conditional = (schema, conditionFunc) => (stream, result, parent, parseFn) => {
    if (conditionFunc(stream, result, parent)) {
        parseFn(stream, schema, result, parent);
    }
};
exports.conditional = conditional;
