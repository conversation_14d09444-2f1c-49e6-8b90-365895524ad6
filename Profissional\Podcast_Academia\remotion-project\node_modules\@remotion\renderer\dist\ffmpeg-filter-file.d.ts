import type { DownloadMap } from './assets/download-map';
import type { FilterWithoutPaddingApplied } from './stringify-ffmpeg-filter';
export declare const makeFfmpegFilterFile: (complexFilter: FilterWithoutPaddingApplied, downloadMap: DownloadMap) => Promise<{
    file: string;
    cleanup: () => void;
}>;
export declare const makeFfmpegFilterFileStr: (complexFilter: string, downloadMap: DownloadMap) => Promise<{
    file: string;
    cleanup: () => void;
}>;
