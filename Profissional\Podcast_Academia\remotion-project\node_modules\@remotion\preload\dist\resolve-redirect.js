"use strict";
/**
 * @description Follows the redirects of a URL (most commonly a remote video or audio) until the final URL is resolved and returns that.
 * @see [Documentation](https://www.remotion.dev/docs/preload/resolve-redirect)
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.resolveRedirect = void 0;
const resolveRedirect = async (videoOrAudio) => {
    const res = await fetch(videoOrAudio);
    return res.url;
};
exports.resolveRedirect = resolveRedirect;
