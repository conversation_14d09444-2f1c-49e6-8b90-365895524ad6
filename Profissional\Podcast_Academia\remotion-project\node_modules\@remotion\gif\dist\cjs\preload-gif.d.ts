/**
 * @description Returns an object with two entries: waitUntilDone() that returns a Promise which can be awaited and free() which will cancel preloading or free up the memory if the GIF is not being used anymore.
 * @see [Documentation](https://www.remotion.dev/docs/gif/preload-gif)
 */
export declare const preloadGif: (src: string) => {
    waitUntilDone: () => Promise<void>;
    free: () => void;
};
