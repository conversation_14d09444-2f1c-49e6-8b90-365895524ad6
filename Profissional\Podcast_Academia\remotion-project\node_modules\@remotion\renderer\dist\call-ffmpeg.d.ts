import execa from 'execa';
import type { SpawnOptionsWithoutStdio } from 'node:child_process';
import type { LogLevel } from './log-level';
import type { CancelSignal } from './make-cancel-signal';
export declare const callFf: ({ args, bin, indent, logLevel, options, binariesDirectory, cancelSignal, }: {
    bin: "ffmpeg" | "ffprobe";
    args: (string | null)[];
    indent: boolean;
    logLevel: LogLevel;
    binariesDirectory: string | null;
    cancelSignal: CancelSignal | undefined;
    options?: execa.Options<string>;
}) => execa.ExecaChildProcess<string>;
export declare const callFfNative: ({ args, bin, indent, logLevel, options, binariesDirectory, cancelSignal, }: {
    bin: "ffmpeg" | "ffprobe";
    args: (string | null)[];
    indent: boolean;
    logLevel: LogLevel;
    binariesDirectory: string | null;
    cancelSignal: CancelSignal | undefined;
    options?: SpawnOptionsWithoutStdio;
}) => import("child_process").ChildProcessWithoutNullStreams;
