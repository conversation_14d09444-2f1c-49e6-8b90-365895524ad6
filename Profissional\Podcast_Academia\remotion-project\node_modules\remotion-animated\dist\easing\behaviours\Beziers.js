import { Easing } from 'remotion';
import CustomBezier from './CustomBezier';
var Beziers;
(function (Beziers) {
    Beziers.Linear = CustomBezier(Easing.linear);
    Beziers.Bounce = CustomBezier(Easing.bounce);
    Beziers.Circle = CustomBezier(Easing.circle);
    Beziers.Quad = CustomBezier(Easing.quad);
    Beziers.Cubic = CustomBezier(Easing.cubic);
    Beziers.Quint = CustomBezier(Easing.poly(5));
    Beziers.Exponential = CustomBezier(Easing.exp);
    Beziers.Sinusoidal = CustomBezier(Easing.sin);
    Beziers.Custom = (x1, y1, x2, y2) => CustomBezier(Easing.bezier(x1, y1, x2, y2));
})(Beziers || (Beziers = {}));
export default Beziers;
