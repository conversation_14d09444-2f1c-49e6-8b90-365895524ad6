import type { GifSchema } from './parser';
export type Stream = {
    data: Uint8Array;
    pos: number;
};
export declare const buildStream: (uint8Data: Uint8Array) => {
    data: Uint8Array<ArrayBufferLike>;
    pos: number;
};
export declare const readByte: () => (stream: Stream) => number;
export declare const peekByte: (offset?: number) => (stream: Stream) => number;
export declare const readBytes: (length: number) => (stream: Stream) => Uint8Array<ArrayBufferLike>;
export declare const peekBytes: (length: number) => (stream: Stream) => Uint8Array<ArrayBufferLike>;
export declare const readString: (length: number) => (stream: Stream) => string;
export declare const readUnsigned: (littleEndian: boolean) => (stream: Stream) => number;
export declare const readArray: <T>(byteSize: number, totalOrFunc: number | ((st: Stream, r: T, p: T) => number)) => (stream: Stream, result: T, parent: T) => any[];
export declare const readBits: (schema: GifSchema) => (stream: Stream) => Record<string, number | boolean>;
