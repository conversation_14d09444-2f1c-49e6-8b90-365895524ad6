"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.makeWorker = void 0;
const source_1 = require("./source");
const makeWorker = () => {
    const blob = new Blob([source_1.src], { type: 'application/javascript' });
    const url = URL.createObjectURL(blob);
    const worker = new Worker(url);
    URL.revokeObjectURL(url);
    return worker;
};
exports.makeWorker = makeWorker;
