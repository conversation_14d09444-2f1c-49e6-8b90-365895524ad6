{"repository": {"url": "https://github.com/remotion-dev/remotion/tree/main/packages/preload"}, "name": "@remotion/preload", "version": "4.0.324", "description": "Preloads assets for use in Remotion", "main": "dist/index.js", "types": "dist/index.d.ts", "sideEffects": false, "author": "<PERSON><PERSON>", "contributors": [], "license": "MIT", "bugs": {"url": "https://github.com/remotion-dev/remotion/issues"}, "keywords": ["remotion", "preload", "html5", "video"], "publishConfig": {"access": "public"}, "devDependencies": {"eslint": "9.19.0", "@remotion/eslint-config-internal": "4.0.324"}, "homepage": "https://www.remotion.dev/docs/preload", "scripts": {"formatting": "prettier --experimental-cli src --check", "lint": "eslint src", "make": "tsc -d"}}