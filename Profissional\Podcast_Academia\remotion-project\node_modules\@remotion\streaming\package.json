{"repository": {"url": "https://github.com/remotion-dev/remotion/tree/main/packages/streaming"}, "name": "@remotion/streaming", "version": "4.0.324", "description": "Utilities for streaming data between programs", "main": "dist", "sideEffects": false, "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": [], "license": "MIT", "bugs": {"url": "https://github.com/remotion-dev/remotion/issues"}, "publishConfig": {"access": "public"}, "devDependencies": {"eslint": "9.19.0", "@remotion/eslint-config-internal": "4.0.324"}, "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "module": "./dist/esm/index.mjs", "import": "./dist/esm/index.mjs"}}, "scripts": {"lint": "eslint src", "formatting": "prettier --experimental-cli src --check", "make": "tsc -d && bun --env-file=../.env.bundle bundle.ts"}}