"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Gif = void 0;
const jsx_runtime_1 = require("react/jsx-runtime");
const react_1 = require("react");
const remotion_1 = require("remotion");
const GifForDevelopment_1 = require("./GifForDevelopment");
const GifForRendering_1 = require("./GifForRendering");
/*
 * @description Displays a GIF that synchronizes with Remotions useCurrentFrame().
 * @see [Documentation](https://remotion.dev/docs/gif)
 */
exports.Gif = (0, react_1.forwardRef)((props, ref) => {
    const env = (0, remotion_1.getRemotionEnvironment)();
    if (env.isRendering) {
        return (0, jsx_runtime_1.jsx)(GifForRendering_1.GifForRendering, { ...props, ref: ref });
    }
    return (0, jsx_runtime_1.jsx)(GifForDevelopment_1.GifForDevelopment, { ...props, ref: ref });
});
