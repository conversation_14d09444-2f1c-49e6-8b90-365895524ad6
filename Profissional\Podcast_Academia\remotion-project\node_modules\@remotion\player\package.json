{"repository": {"url": "https://github.com/remotion-dev/remotion/tree/main/packages/player"}, "name": "@remotion/player", "version": "4.0.324", "description": "React component for embedding a Remotion preview into your app", "main": "dist/cjs/index.js", "types": "dist/cjs/index.d.ts", "module": "dist/esm/index.mjs", "sideEffects": false, "exports": {"./package.json": "./package.json", ".": {"types": "./dist/cjs/index.d.ts", "module": "./dist/esm/index.mjs", "import": "./dist/esm/index.mjs", "require": "./dist/cjs/index.js"}}, "bugs": {"url": "https://github.com/remotion-dev/remotion/issues"}, "author": "<PERSON><PERSON> <<EMAIL>>", "maintainers": ["<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"remotion": "4.0.324"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "devDependencies": {"@testing-library/react": "16.1.0", "@happy-dom/global-registrator": "14.5.1", "csstype": "^3.1.1", "react": "19.0.0", "react-dom": "19.0.0", "webpack": "5.96.1", "zod": "3.22.3", "eslint": "9.19.0", "@remotion/eslint-config-internal": "4.0.324"}, "keywords": ["remotion", "ffmpeg", "video", "react", "player"], "publishConfig": {"access": "public"}, "homepage": "https://www.remotion.dev/docs/player", "scripts": {"formatting": "prettier --experimental-cli src --check", "lint": "eslint src", "make": "tsc -d && bun ensure-correct-version.ts && bun --env-file=../.env.bundle bundle.ts", "test": "bun test src", "prerelease": "cp ../../README.md ."}}