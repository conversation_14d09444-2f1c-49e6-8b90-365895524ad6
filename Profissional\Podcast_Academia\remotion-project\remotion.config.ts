import { Config } from "remotion";

// Configuração global do Remotion
Config.setVideoImageFormat("jpeg");
Config.setOverwriteOutput(true);
Config.setPixelFormat("yuv420p");
Config.setCodec("h264");
Config.setCrf(18);
Config.setQuality(9);

// Configurações para melhor performance
Config.setChromiumUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
Config.setChromiumHeadlessMode(true);
Config.setChromiumOpenGlRenderer("egl");

// Configurações específicas para Instagram
Config.setScale(1);
Config.setMuted(false);
Config.setEnforceAudioTrack(false);

// Configurações de desenvolvimento
if (process.env.NODE_ENV === "development") {
  Config.setPort(3000);
  Config.setPublicDir("./public");
}

// Configurações de produção
if (process.env.NODE_ENV === "production") {
  Config.setOutputLocation("./out");
  Config.setProgress(true);
  Config.setVerbose(true);
}

// Configurações de áudio
Config.setAudioCodec("aac");
Config.setAudioBitrate("128k");

// Configurações de performance
Config.setConcurrency(4);
Config.setDelayRenderTimeoutInMilliseconds(30000);

export {};
