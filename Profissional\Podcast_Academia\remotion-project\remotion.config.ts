import { Config } from "@remotion/cli/config";

// Configuração global do Remotion - QUALIDADE PROFISSIONAL
Config.setVideoImageFormat("png"); // PNG para máxima qualidade
Config.setOverwriteOutput(true);
Config.setPixelFormat("yuv420p");
Config.setCodec("h264");
Config.setCrf(15); // CRF mais baixo para qualidade superior (remove videoBitrate)
Config.setJpegQuality(10); // Qualidade máxima
// Config.setVideoBitrate("12M"); // Comentado - conflita com CRF

// Configurações para melhor performance
Config.setChromiumUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
Config.setChromiumHeadlessMode(true);
Config.setChromiumOpenGlRenderer("egl");

// Configurações específicas para Instagram
Config.setScale(1);
Config.setMuted(false);
Config.setEnforceAudioTrack(false);

// Configurações de desenvolvimento
if (process.env.NODE_ENV === "development") {
  Config.setPort(3000);
  Config.setPublicDir("./public");
}

// Configurações de produção
if (process.env.NODE_ENV === "production") {
  Config.setOutputLocation("./out");
  Config.setProgress(true);
  Config.setVerbose(true);
}

// Configurações de áudio - QUALIDADE PROFISSIONAL
Config.setAudioCodec("aac");
Config.setAudioBitrate("320k"); // Bitrate alto para áudio profissional

// Configurações de performance - OTIMIZADO
Config.setConcurrency(8); // Mais threads para renderização mais rápida
Config.setDelayRenderTimeoutInMilliseconds(60000); // Timeout maior para animações complexas
Config.setTimeoutInMilliseconds(180000); // Timeout geral maior

export {};
