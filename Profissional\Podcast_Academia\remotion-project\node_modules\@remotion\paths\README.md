# @remotion/paths
 
Utilities for working with SVG paths
 
[![NPM Downloads](https://img.shields.io/npm/dm/@remotion/paths.svg?style=flat&color=black&label=Downloads)](https://npmcharts.com/compare/@remotion/paths?minimal=true)
 
## Installation
 
```bash
npm install @remotion/paths --save-exact
```
 
When installing a Remotion package, make sure to align the version of all `remotion` and `@remotion/*` packages to the same version.
Remove the `^` character from the version number to use the exact version.
 
## Usage
 
See the [documentation](https://www.remotion.dev/paths) for more information.
