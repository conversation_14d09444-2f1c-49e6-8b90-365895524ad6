{"repository": {"url": "https://github.com/remotion-dev/remotion/tree/main/packages/gif"}, "name": "@remotion/gif", "version": "4.0.324", "description": "Embed GIFs in a Remotion video", "sideEffects": false, "bugs": {"url": "https://github.com/remotion-dev/remotion/issues"}, "license": "SEE LICENSE IN LICENSE.md", "author": "<PERSON>, <PERSON><PERSON>", "main": "dist/cjs/index.js", "types": "dist/cjs/index.d.ts", "module": "dist/esm/index.mjs", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/cjs/index.d.ts", "module": "./dist/esm/index.mjs", "import": "./dist/esm/index.mjs", "require": "./dist/cjs/index.js"}}, "dependencies": {"remotion": "4.0.324"}, "devDependencies": {"esbuild": "0.25.0", "react": "19.0.0", "react-dom": "19.0.0", "webpack": "5.96.1", "eslint": "9.19.0", "@remotion/eslint-config-internal": "4.0.324"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "keywords": ["remotion", "ffmpeg", "video", "react", "player"], "publishConfig": {"access": "public"}, "homepage": "https://www.remotion.dev/docs/gif", "scripts": {"formatting": "prettier --experimental-cli src --check", "lint": "eslint src", "make": "tsc -d && node build.mjs && bun --env-file=../.env.bundle bundle.ts"}}