import React from "react";
import {
  AbsoluteFill,
  Video,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Audio,
  Artifact,
  staticFile,
  Sequence,
  spring,
} from "remotion";
import { PodcastClip } from "./components/PodcastClip";
import { DynamicSubtitles } from "./components/DynamicSubtitles";
import { BrandingOverlay } from "./components/BrandingOverlay";

interface InstagramPostProps {
  videoPath: string;
  startTime: number;
  duration: number;
  title: string;
  author: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
}

export const InstagramPost: React.FC<InstagramPostProps> = ({
  videoPath,
  startTime,
  duration,
  title,
  author,
  colors
}) => {
  const frame = useCurrentFrame();
  const { fps, durationInFrames } = useVideoConfig();
  
  // Animação de entrada suave para Posts
  const entryAnimation = spring({
    frame,
    fps,
    config: {
      damping: 30,
      stiffness: 100,
    }
  });
  
  // Configuração de layout para formato quadrado
  const videoScale = interpolate(
    frame,
    [0, fps * 2],
    [0.95, 1],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );
  
  // Efeito de rotação sutil para elementos decorativos
  const rotationEffect = interpolate(
    frame,
    [0, durationInFrames],
    [0, 360],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );
  
  return (
    <AbsoluteFill>
      {/* Background gradient */}
      <AbsoluteFill
        style={{
          background: `linear-gradient(135deg, ${colors.primary}, ${colors.secondary})`,
        }}
      />
      
      {/* Padrão decorativo de fundo */}
      <AbsoluteFill
        style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, ${colors.accent}20 2px, transparent 2px),
                           radial-gradient(circle at 75% 75%, ${colors.primary}20 2px, transparent 2px)`,
          backgroundSize: "60px 60px",
          opacity: 0.3,
        }}
      />
      
      {/* Header com título */}
      <div
        style={{
          position: "absolute",
          top: 40,
          left: 40,
          right: 40,
          transform: `scale(${entryAnimation})`,
        }}
      >
        <div
          style={{
            background: `linear-gradient(135deg, ${colors.primary}E6, ${colors.accent}E6)`,
            borderRadius: 20,
            padding: "20px 30px",
            textAlign: "center",
            boxShadow: "0 10px 30px rgba(0,0,0,0.3)",
            border: `3px solid ${colors.text}`,
            backdropFilter: "blur(10px)",
          }}
        >
          <div
            style={{
              fontSize: 32,
              fontWeight: "bold",
              color: colors.text,
              textShadow: "2px 2px 4px rgba(0,0,0,0.5)",
              lineHeight: 1.2,
            }}
          >
            {title}
          </div>
        </div>
      </div>
      
      {/* Vídeo principal centralizado */}
      <div
        style={{
          position: "absolute",
          top: 160,
          left: 60,
          right: 60,
          bottom: 200,
          borderRadius: 20,
          overflow: "hidden",
          boxShadow: "0 20px 40px rgba(0,0,0,0.4)",
          border: `4px solid ${colors.accent}`,
          transform: `scale(${videoScale})`,
        }}
      >
        <PodcastClip
          videoPath={videoPath}
          startTime={startTime}
          duration={duration}
          scale={1}
          colors={colors}
        />
      </div>
      
      {/* Legendas otimizadas para Posts */}
      <div
        style={{
          position: "absolute",
          bottom: 140,
          left: 40,
          right: 40,
          transform: `scale(${entryAnimation})`,
        }}
      >
        <DynamicSubtitles
          videoPath={videoPath}
          startTime={startTime}
          colors={colors}
        />
      </div>
      
      {/* Footer com CTA */}
      <div
        style={{
          position: "absolute",
          bottom: 40,
          left: 40,
          right: 40,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          transform: `scale(${entryAnimation})`,
        }}
      >
        <div
          style={{
            background: `linear-gradient(135deg, ${colors.secondary}E6, ${colors.primary}E6)`,
            borderRadius: 15,
            padding: "12px 20px",
            boxShadow: "0 8px 25px rgba(0,0,0,0.3)",
            border: `2px solid ${colors.accent}`,
          }}
        >
          <div
            style={{
              color: colors.text,
              fontSize: 16,
              fontWeight: "bold",
              textShadow: "1px 1px 2px rgba(0,0,0,0.5)",
            }}
          >
            👥 Siga @{author}
          </div>
        </div>
        
        <div
          style={{
            background: `linear-gradient(135deg, ${colors.accent}E6, ${colors.secondary}E6)`,
            borderRadius: 15,
            padding: "12px 20px",
            boxShadow: "0 8px 25px rgba(0,0,0,0.3)",
            border: `2px solid ${colors.primary}`,
          }}
        >
          <div
            style={{
              color: colors.text,
              fontSize: 16,
              fontWeight: "bold",
              textShadow: "1px 1px 2px rgba(0,0,0,0.5)",
            }}
          >
            💬 Comente
          </div>
        </div>
      </div>
      
      {/* Elementos decorativos rotativos */}
      <div
        style={{
          position: "absolute",
          top: 20,
          right: 20,
          transform: `rotate(${rotationEffect * 0.5}deg)`,
          opacity: 0.6,
          fontSize: 40,
        }}
      >
        🤖
      </div>
      
      <div
        style={{
          position: "absolute",
          bottom: 20,
          left: 20,
          transform: `rotate(${-rotationEffect * 0.3}deg)`,
          opacity: 0.6,
          fontSize: 35,
        }}
      >
        ⚡
      </div>
      
      {/* Indicadores de engagement */}
      <div
        style={{
          position: "absolute",
          top: 120,
          right: 40,
          display: "flex",
          flexDirection: "column",
          gap: 10,
          transform: `scale(${entryAnimation})`,
        }}
      >
        {[
          { emoji: "❤️", count: "1.2K" },
          { emoji: "💬", count: "89" },
          { emoji: "🔄", count: "156" },
        ].map((item, index) => (
          <div
            key={index}
            style={{
              background: `${colors.accent}E6`,
              borderRadius: 12,
              padding: "8px 12px",
              display: "flex",
              alignItems: "center",
              gap: 8,
              boxShadow: "0 4px 15px rgba(0,0,0,0.2)",
              border: `1px solid ${colors.text}`,
            }}
          >
            <span style={{ fontSize: 18 }}>{item.emoji}</span>
            <span
              style={{
                color: colors.text,
                fontSize: 14,
                fontWeight: "bold",
                textShadow: "1px 1px 2px rgba(0,0,0,0.5)",
              }}
            >
              {item.count}
            </span>
          </div>
        ))}
      </div>
      
      {/* Progress indicator */}
      <div
        style={{
          position: "absolute",
          top: 10,
          left: 40,
          right: 40,
          height: 3,
          background: `${colors.text}30`,
          borderRadius: 2,
        }}
      >
        <div
          style={{
            height: "100%",
            background: `linear-gradient(90deg, ${colors.primary}, ${colors.accent})`,
            borderRadius: 2,
            width: `${(frame / durationInFrames) * 100}%`,
            boxShadow: `0 0 6px ${colors.primary}`,
          }}
        />
      </div>
      
      {/* Branding para Posts */}
      <BrandingOverlay
        frame={frame}
        author={author}
        colors={colors}
        position="bottom-left"
      />
      
      {/* Emit Post-specific content */}
      {frame === 0 && (
        <Artifact
          filename="post-metadata.json"
          content={JSON.stringify({
            type: "instagram-post",
            duration: 60,
            title,
            author,
            hashtags: ["#IA", "#AgentesIA", "#Tech", "#Inovação", "#Podcast"],
            engagement: {
              cta: "Siga e comente",
              expectedReach: "5K-10K",
              bestTime: "19:00-21:00"
            },
            description: `${title} 🤖\n\n✨ Descubra como construir agentes IA do zero\n🚀 Transforme sua carreira com tecnologia\n💡 Dicas práticas e estratégias comprovadas\n\n#IA #AgentesIA #Tech #Inovação #Podcast`
          }, null, 2)}
        />
      )}
    </AbsoluteFill>
  );
};
