{"repository": {"url": "https://github.com/remotion-dev/remotion/tree/main/packages/cli"}, "name": "@remotion/cli", "version": "4.0.324", "description": "Control Remotion features using the `npx remotion` command", "main": "dist/index.js", "sideEffects": false, "bin": {"remotion": "remotion-cli.js", "remotionb": "remotionb-cli.js", "remotiond": "remotiond-cli.js"}, "bugs": {"url": "https://github.com/remotion-dev/remotion/issues"}, "exports": {".": "./dist/index.js", "./config": "./dist/config/index.js", "./package.json": "./package.json"}, "typesVersions": {">=1.0": {"config": ["dist/config/index.d.ts"]}}, "author": "<PERSON><PERSON> <<EMAIL>>", "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"dotenv": "9.0.2", "minimist": "1.2.6", "prompts": "2.4.2", "@remotion/media-utils": "4.0.324", "@remotion/bundler": "4.0.324", "@remotion/studio-shared": "4.0.324", "@remotion/player": "4.0.324", "@remotion/renderer": "4.0.324", "@remotion/studio": "4.0.324", "remotion": "4.0.324", "@remotion/studio-server": "4.0.324"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "devDependencies": {"@types/minimist": "^1.2.2", "@types/prompts": "^2.4.1", "@types/prettier": "^2.7.2", "react": "19.0.0", "react-dom": "19.0.0", "zod": "3.22.3", "eslint": "9.19.0", "@remotion/tailwind-v4": "4.0.324", "@remotion/enable-scss": "4.0.324", "@remotion/zod-types": "4.0.324", "@remotion/skia": "4.0.324", "@remotion/eslint-config-internal": "4.0.324"}, "keywords": ["remotion", "ffmpeg", "video", "react", "player"], "publishConfig": {"access": "public"}, "homepage": "https://www.remotion.dev/docs/cli", "scripts": {"formatting": "prettier --experimental-cli src --check", "lint": "eslint src", "test": "bun test src", "make": "tsc -d"}}