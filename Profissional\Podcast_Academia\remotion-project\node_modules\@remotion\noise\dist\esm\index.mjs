// src/index.ts
import { random } from "remotion";
import { createNoise2D, createNoise3D, createNoise4D } from "simplex-noise";
var seedCache2d = new Map;
var seedCache3d = new Map;
var seedCache4d = new Map;
var generate2DNoise = (seed) => {
  const cached = seedCache2d.get(seed);
  if (cached) {
    return cached;
  }
  if (seedCache2d.size > 10) {
    seedCache2d.delete(seedCache2d.keys().next().value);
  }
  const noise = createNoise2D(() => random(seed));
  seedCache2d.set(seed, noise);
  return noise;
};
var generate3DNoise = (seed) => {
  const cached = seedCache3d.get(seed);
  if (cached) {
    return cached;
  }
  if (seedCache3d.size > 10) {
    seedCache3d.delete(seedCache3d.keys().next().value);
  }
  const noise = createNoise3D(() => random(seed));
  seedCache3d.set(seed, noise);
  return noise;
};
var generate4DNoise = (seed) => {
  const cached = seedCache4d.get(seed);
  if (cached) {
    return cached;
  }
  if (seedCache4d.size > 10) {
    seedCache4d.delete(seedCache4d.keys().next().value);
  }
  const noise = createNoise4D(() => random(seed));
  seedCache4d.set(seed, noise);
  return noise;
};
var noise2D = (seed, x, y) => {
  return generate2DNoise(seed)(x, y);
};
var noise3D = (seed, x, y, z) => generate3DNoise(random(seed))(x, y, z);
var noise4D = (seed, x, y, z, w) => generate4DNoise(random(seed))(x, y, z, w);
export {
  noise4D,
  noise3D,
  noise2D
};
