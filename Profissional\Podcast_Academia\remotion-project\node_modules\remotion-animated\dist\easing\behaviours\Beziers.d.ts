declare namespace Beziers {
    const Linear: import("../EasingBehaviour").EasingBehaviour;
    const Bounce: import("../EasingBehaviour").EasingBehaviour;
    const Circle: import("../EasingBehaviour").EasingBehaviour;
    const Quad: import("../EasingBehaviour").EasingBehaviour;
    const Cubic: import("../EasingBehaviour").EasingBehaviour;
    const Quint: import("../EasingBehaviour").EasingBehaviour;
    const Exponential: import("../EasingBehaviour").EasingBehaviour;
    const Sinusoidal: import("../EasingBehaviour").EasingBehaviour;
    const Custom: (x1: number, y1: number, x2: number, y2: number) => import("../EasingBehaviour").EasingBehaviour;
}
export default Beziers;
//# sourceMappingURL=Beziers.d.ts.map