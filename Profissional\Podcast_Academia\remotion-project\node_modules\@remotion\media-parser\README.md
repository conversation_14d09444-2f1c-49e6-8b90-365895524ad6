# @remotion/media-parser
 
A pure JavaScript library for parsing video files
 
[![NPM Downloads](https://img.shields.io/npm/dm/@remotion/media-parser.svg?style=flat&color=black&label=Downloads)](https://npmcharts.com/compare/@remotion/media-parser?minimal=true)
 
## Installation
 
```bash
npm install @remotion/media-parser --save-exact
```
 
When installing a Remotion package, make sure to align the version of all `remotion` and `@remotion/*` packages to the same version.
Remove the `^` character from the version number to use the exact version.
 
## Usage
 
See the [documentation](https://www.remotion.dev/docs/media-parser) for more information.
