# @remotion/media-utils
 
Utilities for working with media files
 
[![NPM Downloads](https://img.shields.io/npm/dm/@remotion/media-utils.svg?style=flat&color=black&label=Downloads)](https://npmcharts.com/compare/@remotion/media-utils?minimal=true)
 
## Installation
 
```bash
npm install @remotion/media-utils --save-exact
```
 
When installing a Remotion package, make sure to align the version of all `remotion` and `@remotion/*` packages to the same version.
Remove the `^` character from the version number to use the exact version.
 
## Usage
 
See the [documentation](https://www.remotion.dev/docs/media-utils) for more information.
