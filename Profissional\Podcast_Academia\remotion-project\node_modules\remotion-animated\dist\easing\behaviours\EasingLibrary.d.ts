declare namespace EasingLibrary {
    const Linear: import("../EasingBehaviour").EasingBehaviour;
    const Bounce: import("../EasingBehaviour").EasingBehaviour;
    const Circle: import("../EasingBehaviour").EasingBehaviour;
    const Quad: import("../EasingBehaviour").EasingBehaviour;
    const Cubic: import("../EasingBehaviour").EasingBehaviour;
    const Quint: import("../EasingBehaviour").EasingBehaviour;
    const Exponential: import("../EasingBehaviour").EasingBehaviour;
    const Sinusoidal: import("../EasingBehaviour").EasingBehaviour;
    const Elastic: (bounciness: number) => import("../EasingBehaviour").EasingBehaviour;
    const Custom: (x1: number, y1: number, x2: number, y2: number) => import("../EasingBehaviour").EasingBehaviour;
}
export default EasingLibrary;
//# sourceMappingURL=EasingLibrary.d.ts.map