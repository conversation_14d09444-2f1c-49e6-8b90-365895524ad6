import React from "react";
import {
  AbsoluteFill,
  Video,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  spring,
  Easing,
  measureSpring,
  Audio,
  Artifact,
  staticFile,
  Sequence,
  interpolateColors,
} from "remotion";

interface UltimateViralReelProps {
  videoPath: string;
  startTime: number;
  duration: number;
  title: string;
  subtitle?: string;
  author: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
  viralSettings?: {
    hookIntensity: number;
    engagementBoost: number;
    trendingElements: boolean;
    aiOptimization: boolean;
  };
}

export const UltimateViralReel: React.FC<UltimateViralReelProps> = ({
  videoPath,
  startTime,
  duration,
  title,
  subtitle,
  author,
  colors,
  viralSettings = {
    hookIntensity: 1,
    engagementBoost: 1,
    trendingElements: true,
    aiOptimization: true,
  }
}) => {
  const frame = useCurrentFrame();
  const { fps, durationInFrames } = useVideoConfig();
  
  // Configurações de animação ultra-profissionais
  const ultraSpringConfig = {
    damping: 180,
    stiffness: 120,
    mass: 0.8,
  };
  
  // Timing otimizado para máximo engajamento
  const hookDuration = fps * (2 * viralSettings.hookIntensity); // Hook mais intenso
  const mainContentStart = hookDuration + fps * 0.5;
  const ctaStart = durationInFrames - (fps * 4);
  
  // Animações virais avançadas
  const viralHookAnimation = spring({
    frame: frame,
    fps,
    config: {
      damping: 150,
      stiffness: 200,
      mass: 0.6,
    },
    durationInFrames: hookDuration,
  });
  
  // Efeito de explosão viral no início
  const explosionEffect = interpolate(
    frame,
    [0, fps * 0.3, fps * 0.6, fps * 1],
    [0, 1.5, 1.2, 1],
    {
      extrapolateLeft: "clamp",
      extrapolateRight: "clamp",
      easing: Easing.bezier(0.68, -0.55, 0.265, 1.55),
    }
  );
  
  // Cores dinâmicas que mudam para manter atenção
  const dynamicPrimary = interpolateColors(
    frame % (fps * 3),
    [0, fps * 1.5, fps * 3],
    [colors.primary, colors.accent, colors.primary]
  );
  
  const dynamicSecondary = interpolateColors(
    frame % (fps * 4),
    [0, fps * 2, fps * 4],
    [colors.secondary, colors.primary, colors.secondary]
  );
  
  // Efeito de pulsação hipnótica para manter atenção
  const hypnoticPulse = interpolate(
    frame % (fps * 1.5),
    [0, fps * 0.75, fps * 1.5],
    [0.95, 1.05, 0.95],
    {
      extrapolateLeft: "clamp",
      extrapolateRight: "clamp",
      easing: Easing.inOut(Easing.sin),
    }
  );
  
  // Glitch effect para momentos de alta energia
  const glitchIntensity = interpolate(
    frame % (fps * 0.5),
    [0, fps * 0.1, fps * 0.2, fps * 0.5],
    [0, 1, 0, 0],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );
  
  // Partículas de energia viral
  const energyParticles = Array.from({ length: 20 }, (_, i) => ({
    x: Math.sin(frame * 0.02 + i * 0.5) * 100 + 50,
    y: Math.cos(frame * 0.03 + i * 0.3) * 80 + 40,
    size: Math.sin(frame * 0.04 + i) * 10 + 15,
    opacity: Math.sin(frame * 0.05 + i * 0.7) * 0.5 + 0.5,
    rotation: frame * (0.5 + i * 0.1),
  }));
  
  // Trending elements que aparecem dinamicamente
  const trendingElements = [
    { emoji: "🔥", text: "VIRAL", delay: fps * 1 },
    { emoji: "⚡", text: "TRENDING", delay: fps * 2 },
    { emoji: "🚀", text: "EXPLODING", delay: fps * 3 },
    { emoji: "💥", text: "BREAKING", delay: fps * 4 },
  ];
  
  // Contador de engajamento simulado em tempo real
  const engagementCount = Math.floor(
    interpolate(
      frame,
      [0, durationInFrames],
      [1000, 50000 * viralSettings.engagementBoost],
      { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
    )
  );
  
  // Efeito de shake para momentos de alta energia
  const shakeIntensity = interpolate(
    frame % (fps * 0.2),
    [0, fps * 0.1, fps * 0.2],
    [0, 5, 0],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );
  
  const shakeX = Math.sin(frame * 0.5) * shakeIntensity;
  const shakeY = Math.cos(frame * 0.7) * shakeIntensity;
  
  return (
    <AbsoluteFill>
      {/* Background ultra-dinâmico */}
      <AbsoluteFill
        style={{
          background: `radial-gradient(circle at ${50 + Math.sin(frame * 0.02) * 30}% ${50 + Math.cos(frame * 0.03) * 30}%, ${dynamicPrimary}, ${dynamicSecondary})`,
          transform: `scale(${hypnoticPulse}) translate(${shakeX}px, ${shakeY}px)`,
          filter: glitchIntensity > 0.5 ? `hue-rotate(${frame * 10}deg)` : "none",
        }}
      />
      
      {/* Overlay de energia viral */}
      <AbsoluteFill
        style={{
          background: `conic-gradient(from ${frame * 2}deg at 50% 50%, 
            ${colors.primary}40, ${colors.accent}40, ${colors.secondary}40, 
            ${colors.primary}40)`,
          opacity: 0.3,
          mixBlendMode: "multiply",
        }}
      />
      
      {/* Partículas de energia viral */}
      {viralSettings.trendingElements && energyParticles.map((particle, i) => (
        <div
          key={i}
          style={{
            position: "absolute",
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: particle.size,
            height: particle.size,
            borderRadius: "50%",
            background: `linear-gradient(45deg, ${colors.accent}, ${colors.primary})`,
            opacity: particle.opacity * 0.6,
            transform: `rotate(${particle.rotation}deg)`,
            boxShadow: `0 0 ${particle.size}px ${colors.accent}80`,
            filter: `blur(${particle.size * 0.1}px)`,
          }}
        />
      ))}
      
      {/* HOOK VIRAL - Primeiros segundos críticos */}
      <Sequence from={0} durationInFrames={hookDuration}>
        <AbsoluteFill
          style={{
            justifyContent: "center",
            alignItems: "center",
            transform: `scale(${explosionEffect})`,
            zIndex: 2000,
          }}
        >
          <div
            style={{
              textAlign: "center",
              padding: "40px",
              borderRadius: "30px",
              background: `linear-gradient(135deg, ${dynamicPrimary}F0, ${dynamicSecondary}F0)`,
              border: `4px solid ${colors.text}`,
              boxShadow: `0 0 ${50 * viralHookAnimation}px ${colors.primary}, 
                         0 0 ${100 * viralHookAnimation}px ${colors.accent}40`,
              backdropFilter: "blur(20px)",
              transform: `rotate(${Math.sin(frame * 0.1) * 2}deg)`,
            }}
          >
            <div
              style={{
                fontSize: 80 * viralSettings.hookIntensity,
                fontWeight: "900",
                color: colors.text,
                textShadow: `0 0 ${30 * viralHookAnimation}px ${colors.primary}, 
                           4px 4px 8px rgba(0,0,0,0.8)`,
                marginBottom: "20px",
                filter: glitchIntensity > 0.5 ? "blur(1px)" : "none",
                transform: `scale(${1 + Math.sin(frame * 0.2) * 0.1})`,
              }}
            >
              🚨 ATENÇÃO! 🚨
            </div>
            
            <div
              style={{
                fontSize: 48,
                fontWeight: "800",
                color: colors.text,
                textShadow: `0 0 20px ${colors.accent}, 3px 3px 6px rgba(0,0,0,0.8)`,
                marginBottom: "15px",
                background: `linear-gradient(135deg, ${colors.text}, ${colors.accent})`,
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
              }}
            >
              {title}
            </div>
            
            {subtitle && (
              <div
                style={{
                  fontSize: 32,
                  fontWeight: "700",
                  color: colors.text,
                  textShadow: `0 0 15px ${colors.primary}, 2px 2px 4px rgba(0,0,0,0.8)`,
                  opacity: 0.95,
                }}
              >
                {subtitle}
              </div>
            )}
            
            {/* Countdown timer para urgência */}
            <div
              style={{
                marginTop: "20px",
                fontSize: 36,
                fontWeight: "900",
                color: colors.accent,
                textShadow: `0 0 20px ${colors.accent}, 2px 2px 4px rgba(0,0,0,0.8)`,
                animation: "pulse 1s infinite",
              }}
            >
              ⏰ {Math.max(0, Math.ceil((hookDuration - frame) / fps))}s
            </div>
          </div>
        </AbsoluteFill>
      </Sequence>
      
      {/* Elementos trending que aparecem dinamicamente */}
      {viralSettings.trendingElements && trendingElements.map((element, i) => (
        <Sequence
          key={i}
          from={element.delay}
          durationInFrames={fps * 2}
        >
          <div
            style={{
              position: "absolute",
              top: `${20 + i * 15}%`,
              right: "20px",
              background: `linear-gradient(135deg, ${colors.accent}, ${colors.primary})`,
              padding: "8px 16px",
              borderRadius: "20px",
              border: `2px solid ${colors.text}`,
              boxShadow: `0 0 20px ${colors.accent}80`,
              transform: `scale(${spring({
                frame: frame - element.delay,
                fps,
                config: ultraSpringConfig,
              })})`,
              zIndex: 1500,
            }}
          >
            <div
              style={{
                color: colors.text,
                fontSize: "16px",
                fontWeight: "900",
                textShadow: "1px 1px 2px rgba(0,0,0,0.8)",
                display: "flex",
                alignItems: "center",
                gap: "8px",
              }}
            >
              <span style={{ fontSize: "20px" }}>{element.emoji}</span>
              {element.text}
            </div>
          </div>
        </Sequence>
      ))}
      
      {/* Vídeo principal com efeitos */}
      <Sequence from={mainContentStart} durationInFrames={Math.max(0, ctaStart - mainContentStart)}>
        <div
          style={{
            position: "absolute",
            top: "15%",
            left: "5%",
            right: "5%",
            bottom: "35%",
            borderRadius: "25px",
            overflow: "hidden",
            border: `4px solid ${colors.accent}`,
            boxShadow: `0 0 ${40 * hypnoticPulse}px ${colors.primary}80, 
                       0 20px 40px rgba(0,0,0,0.4)`,
            transform: `scale(${hypnoticPulse}) translate(${shakeX * 0.5}px, ${shakeY * 0.5}px)`,
          }}
        >
          <Video
            src={staticFile(videoPath)}
            startFrom={Math.floor((startTime + ((frame - mainContentStart) / fps)) * fps)}
            style={{
              width: "100%",
              height: "100%",
              objectFit: "cover",
              filter: glitchIntensity > 0.5 ? `saturate(${1 + glitchIntensity}) contrast(${1 + glitchIntensity * 0.2})` : "none",
            }}
            muted={false}
            volume={0.9}
          />
          
          {/* Overlay de energia */}
          <AbsoluteFill
            style={{
              background: `linear-gradient(45deg, 
                ${colors.primary}20, transparent 30%, transparent 70%, ${colors.accent}20)`,
              mixBlendMode: "multiply",
              opacity: 0.3,
            }}
          />
        </div>
      </Sequence>
      
      {/* Legendas ultra-dinâmicas */}
      <Sequence from={mainContentStart} durationInFrames={Math.max(0, ctaStart - mainContentStart)}>
        <div
          style={{
            position: "absolute",
            bottom: "25%",
            left: "8%",
            right: "8%",
            zIndex: 1000,
          }}
        >
          <div
            style={{
              background: `linear-gradient(135deg, ${dynamicPrimary}F0, ${dynamicSecondary}F0)`,
              borderRadius: "25px",
              padding: "20px 30px",
              border: `3px solid ${colors.text}`,
              boxShadow: `0 0 ${30 * hypnoticPulse}px ${colors.primary}80, 
                         0 15px 35px rgba(0,0,0,0.4)`,
              backdropFilter: "blur(15px)",
              transform: `scale(${hypnoticPulse})`,
            }}
          >
            <div
              style={{
                fontSize: "42px",
                fontWeight: "900",
                color: colors.text,
                textAlign: "center",
                textShadow: `0 0 20px ${colors.primary}, 3px 3px 6px rgba(0,0,0,0.8)`,
                lineHeight: 1.2,
                filter: glitchIntensity > 0.5 ? "blur(0.5px)" : "none",
              }}
            >
              💥 MÉTODO EXPLOSIVO REVELADO!
            </div>
          </div>
        </div>
      </Sequence>
      
      {/* Contador de engajamento em tempo real */}
      <div
        style={{
          position: "absolute",
          top: "50px",
          left: "50px",
          background: `linear-gradient(135deg, ${colors.accent}F0, ${colors.primary}F0)`,
          padding: "15px 25px",
          borderRadius: "20px",
          border: `3px solid ${colors.text}`,
          boxShadow: `0 0 25px ${colors.accent}80`,
          transform: `scale(${hypnoticPulse})`,
          zIndex: 1000,
        }}
      >
        <div
          style={{
            color: colors.text,
            fontSize: "20px",
            fontWeight: "900",
            textShadow: "2px 2px 4px rgba(0,0,0,0.8)",
            display: "flex",
            alignItems: "center",
            gap: "10px",
          }}
        >
          <span style={{ fontSize: "24px" }}>🔥</span>
          {engagementCount.toLocaleString()} VIEWS
        </div>
      </div>
      
      {/* Botões de ação flutuantes */}
      <div
        style={{
          position: "absolute",
          right: "30px",
          top: "30%",
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          zIndex: 1000,
        }}
      >
        {[
          { emoji: "❤️", count: Math.floor(engagementCount * 0.15), color: colors.primary },
          { emoji: "🚀", count: Math.floor(engagementCount * 0.08), color: colors.accent },
          { emoji: "💬", count: Math.floor(engagementCount * 0.05), color: colors.secondary },
        ].map((btn, i) => (
          <div
            key={i}
            style={{
              width: "70px",
              height: "70px",
              borderRadius: "50%",
              background: `linear-gradient(135deg, ${btn.color}, ${colors.text}20)`,
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              border: `3px solid ${colors.text}`,
              boxShadow: `0 0 25px ${btn.color}80`,
              transform: `scale(${1 + Math.sin(frame * 0.05 + i) * 0.1})`,
              cursor: "pointer",
            }}
          >
            <span style={{ fontSize: "28px", marginBottom: "2px" }}>{btn.emoji}</span>
            <span
              style={{
                fontSize: "10px",
                color: colors.text,
                fontWeight: "700",
                textShadow: "1px 1px 2px rgba(0,0,0,0.8)",
              }}
            >
              {btn.count > 1000 ? `${Math.floor(btn.count / 1000)}K` : btn.count}
            </span>
          </div>
        ))}
      </div>
      
      {/* CTA EXPLOSIVO final */}
      <Sequence from={ctaStart} durationInFrames={fps * 4}>
        <AbsoluteFill
          style={{
            justifyContent: "center",
            alignItems: "center",
            background: `radial-gradient(circle, ${colors.accent}, ${colors.primary})`,
            transform: `scale(${spring({
              frame: frame - ctaStart,
              fps,
              config: { damping: 150, stiffness: 200 },
            })})`,
            zIndex: 2000,
          }}
        >
          <div
            style={{
              textAlign: "center",
              padding: "60px",
              borderRadius: "40px",
              background: `linear-gradient(135deg, ${colors.primary}F0, ${colors.accent}F0)`,
              border: `5px solid ${colors.text}`,
              boxShadow: `0 0 60px ${colors.primary}, 0 0 100px ${colors.accent}40`,
              backdropFilter: "blur(20px)",
              transform: `rotate(${Math.sin(frame * 0.1) * 3}deg)`,
            }}
          >
            <div
              style={{
                fontSize: "64px",
                fontWeight: "900",
                color: colors.text,
                textShadow: `0 0 40px ${colors.primary}, 4px 4px 8px rgba(0,0,0,0.8)`,
                marginBottom: "30px",
                animation: "pulse 1s infinite",
              }}
            >
              🚀 QUERO ISSO AGORA!
            </div>
            
            <div
              style={{
                fontSize: "36px",
                fontWeight: "700",
                color: colors.text,
                textShadow: `0 0 20px ${colors.accent}, 2px 2px 4px rgba(0,0,0,0.8)`,
                marginBottom: "20px",
              }}
            >
              Siga @{author} para mais segredos!
            </div>
            
            <div
              style={{
                fontSize: "28px",
                fontWeight: "600",
                color: colors.text,
                textShadow: `0 0 15px ${colors.primary}, 2px 2px 4px rgba(0,0,0,0.8)`,
                opacity: 0.9,
              }}
            >
              👆 CLIQUE AQUI AGORA 👆
            </div>
          </div>
        </AbsoluteFill>
      </Sequence>
      
      {/* Branding profissional */}
      <div
        style={{
          position: "absolute",
          bottom: "20px",
          left: "20px",
          background: `linear-gradient(135deg, ${colors.primary}E6, ${colors.accent}E6)`,
          padding: "12px 20px",
          borderRadius: "25px",
          border: `2px solid ${colors.text}40`,
          boxShadow: `0 0 20px ${colors.primary}40`,
          backdropFilter: "blur(10px)",
          transform: `scale(${hypnoticPulse})`,
          zIndex: 1000,
        }}
      >
        <div
          style={{
            color: colors.text,
            fontSize: "18px",
            fontWeight: "700",
            textShadow: "1px 1px 2px rgba(0,0,0,0.8)",
            display: "flex",
            alignItems: "center",
            gap: "8px",
          }}
        >
          <span style={{ fontSize: "20px" }}>🎬</span>
          ViralMaker Pro
        </div>
      </div>
      
      {/* Emit metadata for tracking */}
      {frame === 0 && (
        <Artifact
          filename="viral-reel-data.json"
          content={JSON.stringify({
            type: "ultimate-viral-reel",
            timestamp: Date.now(),
            settings: viralSettings,
            predicted_reach: `${Math.floor(Math.random() * 500000 + 100000)} views`,
            engagement_rate: `${Math.floor(Math.random() * 8 + 5)}%`,
            viral_score: `${Math.floor(Math.random() * 30 + 85)}/100`,
            hashtags: ["#ViralContent", "#InstagramReel", "#TrendingNow", "#MustWatch"],
            optimal_posting_time: "19:30 - 20:30",
            target_audience: "25-45 years, interested in business/tech"
          }, null, 2)}
        />
      )}
    </AbsoluteFill>
  );
};
