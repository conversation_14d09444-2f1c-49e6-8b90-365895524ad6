{"repository": {"url": "https://github.com/remotion-dev/remotion/tree/main/packages/licensing"}, "name": "@remotion/licensing", "version": "4.0.324", "description": "Manage your Remotion.pro license", "main": "dist", "sideEffects": false, "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": [], "license": "MIT", "bugs": {"url": "https://github.com/remotion-dev/remotion/issues"}, "publishConfig": {"access": "public"}, "dependencies": {}, "devDependencies": {"eslint": "9.19.0", "@remotion/eslint-config-internal": "4.0.324"}, "homepage": "https://www.remotion.dev/docs/licensing", "scripts": {"lint": "eslint src", "formatting": "prettier --experimental-cli src --check", "make": "tsc -d && bun --env-file=../.env.bundle bundle.ts", "test": "bun test src/test/prod-domain.test.ts"}}