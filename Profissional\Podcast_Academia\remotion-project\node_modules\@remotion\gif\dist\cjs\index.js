"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.preloadGif = exports.Gif = exports.getGifDurationInSeconds = void 0;
var get_gif_duration_in_seconds_1 = require("./get-gif-duration-in-seconds");
Object.defineProperty(exports, "getGifDurationInSeconds", { enumerable: true, get: function () { return get_gif_duration_in_seconds_1.getGifDurationInSeconds; } });
var Gif_1 = require("./Gif");
Object.defineProperty(exports, "Gif", { enumerable: true, get: function () { return Gif_1.Gif; } });
var preload_gif_1 = require("./preload-gif");
Object.defineProperty(exports, "preloadGif", { enumerable: true, get: function () { return preload_gif_1.preloadGif; } });
