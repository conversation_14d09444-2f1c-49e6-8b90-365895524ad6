import type { ParsedGif } from '../gifuct/types';
import type { Stream } from './uint8-parser';
export declare const GIF: (((stream: Stream, result: ParsedGif, parent: ParsedGif, parseFn: (st: Stream, schema: import("./parser").GifSchema, result: ParsedGif, parent: ParsedGif) => void) => void) | {
    header: ({
        signature: (stream: Stream) => string;
        version?: undefined;
    } | {
        version: (stream: Stream) => string;
        signature?: undefined;
    })[];
    lsd?: undefined;
    frames?: undefined;
} | {
    lsd: ({
        width: (stream: Stream) => number;
        height?: undefined;
        gct?: undefined;
        backgroundColorIndex?: undefined;
        pixelAspectRatio?: undefined;
    } | {
        height: (stream: Stream) => number;
        width?: undefined;
        gct?: undefined;
        backgroundColorIndex?: undefined;
        pixelAspectRatio?: undefined;
    } | {
        gct: (stream: Stream) => Record<string, number | boolean>;
        width?: undefined;
        height?: undefined;
        backgroundColorIndex?: undefined;
        pixelAspectRatio?: undefined;
    } | {
        backgroundColorIndex: (stream: Stream) => number;
        width?: undefined;
        height?: undefined;
        gct?: undefined;
        pixelAspectRatio?: undefined;
    } | {
        pixelAspectRatio: (stream: Stream) => number;
        width?: undefined;
        height?: undefined;
        gct?: undefined;
        backgroundColorIndex?: undefined;
    })[];
    header?: undefined;
    frames?: undefined;
} | {
    frames: (stream: Stream, result: unknown, parent: unknown, _parse: (st: Stream, schema: import("./parser").GifSchema, result: unknown, parent: unknown) => void) => unknown[];
    header?: undefined;
    lsd?: undefined;
})[];
