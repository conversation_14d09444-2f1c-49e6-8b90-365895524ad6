"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const bun_test_1 = require("bun:test");
const utils_1 = require("../utils");
(0, bun_test_1.describe)('getNextFrame', () => {
    (0, bun_test_1.describe)('when loop is falsy', () => {
        (0, bun_test_1.it)('returns the current frame if smaller than total frames', () => {
            (0, bun_test_1.expect)((0, utils_1.getLottieFrame)({ currentFrame: 23, totalFrames: 56 })).toBe(23);
        });
        (0, bun_test_1.it)('returns the last frame if current frame is bigger than total frames', () => {
            (0, bun_test_1.expect)((0, utils_1.getLottieFrame)({ currentFrame: 23, totalFrames: 20 })).toBe(19);
        });
        (0, bun_test_1.it)('freezes on last valid frame to prevent animation disappearing', () => {
            // This test specifically validates the fix for the issue where
            // non-looping animations disappear after playing once
            (0, bun_test_1.expect)((0, utils_1.getLottieFrame)({ currentFrame: 100, totalFrames: 30 })).toBe(29);
            (0, bun_test_1.expect)((0, utils_1.getLottieFrame)({ currentFrame: 1000, totalFrames: 60 })).toBe(59);
        });
    });
    (0, bun_test_1.describe)('when loop is truthy', () => {
        (0, bun_test_1.it)('returns the current frame if smaller than total frames', () => {
            (0, bun_test_1.expect)((0, utils_1.getLottieFrame)({ currentFrame: 23, totalFrames: 56, loop: true })).toBe(23);
        });
        (0, bun_test_1.it)('returns the modulo if current frame is bigger than total frames', () => {
            (0, bun_test_1.expect)((0, utils_1.getLottieFrame)({ currentFrame: 23, totalFrames: 20, loop: true })).toBe(3);
        });
    });
    (0, bun_test_1.describe)('when direction is reverse and loop is falsy', () => {
        (0, bun_test_1.it)('returns the correct frame if current frame is smaller than total frames', () => {
            (0, bun_test_1.expect)((0, utils_1.getLottieFrame)({
                currentFrame: 15,
                totalFrames: 20,
                direction: 'backward',
            })).toBe(5);
        });
        (0, bun_test_1.it)('returns frame zero if current frame is bigger than total frames', () => {
            (0, bun_test_1.expect)((0, utils_1.getLottieFrame)({
                currentFrame: 23,
                totalFrames: 20,
                direction: 'backward',
            })).toBe(1);
        });
    });
    (0, bun_test_1.describe)('when direction is reverse and loop is truthy', () => {
        (0, bun_test_1.it)('returns the correct frame if current frame is smaller than total frames', () => {
            (0, bun_test_1.expect)((0, utils_1.getLottieFrame)({
                currentFrame: 15,
                totalFrames: 20,
                direction: 'backward',
                loop: true,
            })).toBe(5);
        });
        (0, bun_test_1.it)('returns (total-overflow) if current frame is bigger than total frames', () => {
            (0, bun_test_1.expect)((0, utils_1.getLottieFrame)({
                currentFrame: 23,
                totalFrames: 20,
                direction: 'backward',
                loop: true,
            })).toBe(17);
        });
    });
});
