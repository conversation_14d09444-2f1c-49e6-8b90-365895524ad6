import type { DownloadMap } from './assets/download-map';
import type { MediaAsset } from './assets/types';
import type { LogLevel } from './log-level';
import type { CancelSignal } from './make-cancel-signal';
import type { ProcessedTrack } from './stringify-ffmpeg-filter';
type Options = {
    outName: string;
    asset: MediaAsset;
    fps: number;
    downloadMap: DownloadMap;
    indent: boolean;
    logLevel: LogLevel;
    binariesDirectory: string | null;
    cancelSignal: CancelSignal | undefined;
    chunkLengthInSeconds: number;
    trimLeftOffset: number;
    trimRightOffset: number;
    forSeamlessAacConcatenation: boolean;
    onProgress: (progress: number) => void;
};
export type PreprocessedAudioTrack = {
    outName: string;
    filter: ProcessedTrack;
};
export declare const preprocessAudioTrack: (options: Options) => Promise<PreprocessedAudioTrack | null>;
export {};
