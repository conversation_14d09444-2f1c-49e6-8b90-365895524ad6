import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { Animated, Move, Fade, Scale, Rotate } from 'remotion-animated';

interface SocialOverlayProps {
  author: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
  };
  showCTA?: boolean;
}

export const SocialOverlay: React.FC<SocialOverlayProps> = ({
  author,
  colors,
  showCTA = true,
}) => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();

  // Animações de entrada escalonadas
  const logoOpacity = interpolate(
    frame,
    [60, 90],
    [0, 1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  const ctaOpacity = interpolate(
    frame,
    [durationInFrames - 120, durationInFrames - 60],
    [0, 1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  // Efeito de pulsação para CTA
  const ctaPulse = interpolate(
    frame,
    [0, 30, 60],
    [1, 1.05, 1],
    { extrapolateRight: 'mirror' }
  );

  // Rotação sutil para elementos decorativos
  const decorRotation = interpolate(
    frame,
    [0, durationInFrames],
    [0, 360],
    { extrapolateRight: 'clamp' }
  );

  return (
    <>
      {/* Logo/Branding no canto superior */}
      <Animated
        animations={[
          Fade({ to: logoOpacity, initial: 0 }),
          Move({ x: -20, start: 60 }),
          Scale({ by: 1, initial: 0.8, start: 60 }),
        ]}
      >
        <div
          style={{
            position: 'absolute',
            top: 30,
            right: 30,
            padding: '15px 25px',
            background: `linear-gradient(135deg, ${colors.primary}CC, ${colors.accent}CC)`,
            backdropFilter: 'blur(10px)',
            borderRadius: '25px',
            border: `2px solid ${colors.accent}40`,
            zIndex: 15,
          }}
        >
          <div
            style={{
              fontSize: '1.2rem',
              fontWeight: 'bold',
              color: 'white',
              textShadow: '0 2px 10px rgba(0,0,0,0.5)',
            }}
          >
            @{author}
          </div>
        </div>
      </Animated>

      {/* CTA Principal */}
      {showCTA && (
        <Animated
          animations={[
            Fade({ to: ctaOpacity, initial: 0 }),
            Scale({ by: ctaPulse, initial: 0.9 }),
            Move({ y: 20, start: durationInFrames - 120 }),
          ]}
        >
          <div
            style={{
              position: 'absolute',
              bottom: 100,
              left: '50%',
              transform: 'translateX(-50%)',
              textAlign: 'center',
              zIndex: 15,
            }}
          >
            {/* Botão CTA principal */}
            <div
              style={{
                padding: '20px 40px',
                background: `linear-gradient(45deg, ${colors.accent}, ${colors.primary})`,
                borderRadius: '50px',
                border: '3px solid white',
                boxShadow: '0 10px 30px rgba(0,0,0,0.3)',
                cursor: 'pointer',
                transform: `scale(${ctaPulse})`,
                marginBottom: '15px',
              }}
            >
              <div
                style={{
                  fontSize: '1.5rem',
                  fontWeight: 'bold',
                  color: 'white',
                  textShadow: '0 2px 10px rgba(0,0,0,0.5)',
                }}
              >
                🚀 APRENDA AGORA!
              </div>
            </div>

            {/* Texto secundário */}
            <div
              style={{
                fontSize: '1rem',
                color: colors.secondary,
                fontWeight: '500',
                textShadow: '0 2px 10px rgba(0,0,0,0.8)',
                background: 'rgba(0,0,0,0.5)',
                padding: '10px 20px',
                borderRadius: '20px',
                backdropFilter: 'blur(5px)',
              }}
            >
              Construa seu primeiro Agente IA em 90 dias
            </div>
          </div>
        </Animated>
      )}

      {/* Elementos decorativos animados */}
      <Animated
        animations={[
          Rotate({ degrees: decorRotation }),
          Fade({ to: 0.3, initial: 0, start: 30 }),
        ]}
      >
        <div
          style={{
            position: 'absolute',
            top: '20%',
            right: '10%',
            width: '60px',
            height: '60px',
            border: `3px solid ${colors.accent}60`,
            borderRadius: '50%',
            transform: `rotate(${decorRotation}deg)`,
          }}
        />
      </Animated>

      <Animated
        animations={[
          Rotate({ degrees: -decorRotation * 0.7 }),
          Fade({ to: 0.2, initial: 0, start: 45 }),
        ]}
      >
        <div
          style={{
            position: 'absolute',
            bottom: '30%',
            left: '8%',
            width: '40px',
            height: '40px',
            background: `linear-gradient(45deg, ${colors.primary}40, ${colors.accent}40)`,
            borderRadius: '8px',
            transform: `rotate(${-decorRotation * 0.7}deg)`,
          }}
        />
      </Animated>

      {/* Partículas flutuantes */}
      {[...Array(5)].map((_, i) => (
        <Animated
          key={i}
          animations={[
            Move({ 
              y: -100, 
              start: i * 20,
              ease: 'easeOut'
            }),
            Fade({ 
              to: 0, 
              initial: 0.6, 
              start: i * 20 + 60 
            }),
          ]}
        >
          <div
            style={{
              position: 'absolute',
              left: `${20 + i * 15}%`,
              bottom: '10%',
              width: '8px',
              height: '8px',
              background: colors.accent,
              borderRadius: '50%',
              opacity: 0.6,
            }}
          />
        </Animated>
      ))}
    </>
  );
};
