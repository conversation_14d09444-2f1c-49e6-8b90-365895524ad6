"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RenameComposition = void 0;
const jsx_runtime_1 = require("react/jsx-runtime");
const react_1 = require("react");
const remotion_1 = require("remotion");
const validate_new_comp_data_1 = require("../../helpers/validate-new-comp-data");
const ModalFooter_1 = require("../ModalFooter");
const ModalHeader_1 = require("../ModalHeader");
const ResolveCompositionBeforeModal_1 = require("../RenderModal/ResolveCompositionBeforeModal");
const layout_1 = require("../RenderModal/layout");
const layout_2 = require("../layout");
const CodemodFooter_1 = require("./CodemodFooter");
const DismissableModal_1 = require("./DismissableModal");
const RemInput_1 = require("./RemInput");
const ValidationMessage_1 = require("./ValidationMessage");
const content = {
    padding: 12,
    paddingRight: 12,
    flex: 1,
    fontSize: 13,
    minWidth: 500,
};
const RenameCompositionLoaded = () => {
    const context = (0, react_1.useContext)(ResolveCompositionBeforeModal_1.ResolvedCompositionContext);
    if (!context) {
        throw new Error('Resolved composition context');
    }
    const { resolved } = context;
    const { compositions } = (0, react_1.useContext)(remotion_1.Internals.CompositionManager);
    const [newId, setName] = (0, react_1.useState)(() => {
        return resolved.result.id;
    });
    const onNameChange = (0, react_1.useCallback)((e) => {
        setName(e.target.value);
    }, []);
    const compNameErrMessage = newId === resolved.result.id
        ? null
        : (0, validate_new_comp_data_1.validateCompositionName)(newId, compositions);
    const valid = compNameErrMessage === null && resolved.result.id !== newId;
    const codemod = (0, react_1.useMemo)(() => {
        return {
            type: 'rename-composition',
            idToRename: resolved.result.id,
            newId,
        };
    }, [newId, resolved.result.id]);
    const onSubmit = (0, react_1.useCallback)((e) => {
        e.preventDefault();
    }, []);
    return ((0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, { children: [(0, jsx_runtime_1.jsx)(ModalHeader_1.ModalHeader, { title: `Rename ${resolved.result.id}` }), (0, jsx_runtime_1.jsxs)("form", { onSubmit: onSubmit, children: [(0, jsx_runtime_1.jsx)("div", { style: content, children: (0, jsx_runtime_1.jsxs)("div", { style: layout_1.optionRow, children: [(0, jsx_runtime_1.jsx)("div", { style: layout_1.label, children: "ID" }), (0, jsx_runtime_1.jsx)("div", { style: layout_1.rightRow, children: (0, jsx_runtime_1.jsxs)("div", { children: [(0, jsx_runtime_1.jsx)(RemInput_1.RemotionInput, { value: newId, onChange: onNameChange, type: "text", autoFocus: true, placeholder: "Composition ID", status: "ok", rightAlign: true }), compNameErrMessage ? ((0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, { children: [(0, jsx_runtime_1.jsx)(layout_2.Spacing, { y: 1, block: true }), (0, jsx_runtime_1.jsx)(ValidationMessage_1.ValidationMessage, { align: "flex-start", message: compNameErrMessage, type: "error" })] })) : null] }) })] }) }), (0, jsx_runtime_1.jsx)(ModalFooter_1.ModalFooterContainer, { children: (0, jsx_runtime_1.jsx)(CodemodFooter_1.CodemodFooter, { loadingNotification: 'Renaming...', errorNotification: 'Could not rename composition', successNotification: `Renamed to ${newId}`, genericSubmitLabel: 'Rename', submitLabel: ({ relativeRootPath }) => `Modify ${relativeRootPath}`, codemod: codemod, valid: valid }) })] })] }));
};
const RenameComposition = ({ compositionId }) => {
    return ((0, jsx_runtime_1.jsx)(DismissableModal_1.DismissableModal, { children: (0, jsx_runtime_1.jsx)(ResolveCompositionBeforeModal_1.ResolveCompositionBeforeModal, { compositionId: compositionId, children: (0, jsx_runtime_1.jsx)(RenameCompositionLoaded, {}) }) }));
};
exports.RenameComposition = RenameComposition;
