#!/bin/bash

# 🚀 Script de Instalação Rápida - Sistema de Edição Profissional para Instagram

echo "🎬 SISTEMA DE EDIÇÃO PROFISSIONAL PARA INSTAGRAM"
echo "================================================="
echo ""

# Verificar se Node.js está instalado
if ! command -v node &> /dev/null; then
    echo "❌ Node.js não encontrado. Instale o Node.js primeiro."
    exit 1
fi

echo "✅ Node.js encontrado: $(node --version)"

# Verificar se npm está instalado
if ! command -v npm &> /dev/null; then
    echo "❌ npm não encontrado. Instale o npm primeiro."
    exit 1
fi

echo "✅ npm encontrado: $(npm --version)"

# Verificar se FFmpeg está instalado
if ! command -v ffmpeg &> /dev/null; then
    echo "⚠️  FFmpeg não encontrado. Tentando instalar..."
    # Tentar instalar no Windows
    if command -v choco &> /dev/null; then
        choco install ffmpeg
    elif command -v winget &> /dev/null; then
        winget install FFmpeg
    else
        echo "❌ Instale o FFmpeg manualmente de https://ffmpeg.org/download.html"
        exit 1
    fi
else
    echo "✅ FFmpeg encontrado: $(ffmpeg -version | head -n 1)"
fi

echo ""
echo "📦 Instalando dependências..."
echo ""

# Instalar dependências do npm
npm install

echo ""
echo "🔧 Instalando Remotion CLI globalmente..."
echo ""

# Instalar Remotion CLI
npm install -g @remotion/cli

echo ""
echo "🎯 Configuração concluída!"
echo ""
echo "📋 PRÓXIMOS PASSOS:"
echo ""
echo "1. Para testar a visualização:"
echo "   npm run dev"
echo ""
echo "2. Para renderizar um vídeo de teste:"
echo "   npm run render:reels"
echo ""
echo "3. Para renderizar todos os vídeos:"
echo "   npm run render:all"
echo ""
echo "4. Para editar as configurações:"
echo "   - Cores e temas: src/index.ts"
echo "   - Vídeos: scripts/render-all.js"
echo "   - Legendas: src/components/DynamicSubtitles.tsx"
echo ""
echo "📁 Os vídeos serão salvos na pasta: output/"
echo ""
echo "🎉 Sistema pronto para uso!"
echo ""
echo "💡 Dica: Coloque seus vídeos de podcast na pasta principal"
echo "   e execute 'npm run render:all' para gerar conteúdo viral!"
