import { Easing } from 'remotion';
import CustomEasing from './CustomEasing';
var EasingLibrary;
(function (EasingLibrary) {
    EasingLibrary.Linear = CustomEasing(Easing.linear);
    EasingLibrary.Bounce = CustomEasing(Easing.bounce);
    EasingLibrary.Circle = CustomEasing(Easing.circle);
    EasingLibrary.Quad = CustomEasing(Easing.quad);
    EasingLibrary.Cubic = CustomEasing(Easing.cubic);
    EasingLibrary.Quint = CustomEasing(Easing.poly(5));
    EasingLibrary.Exponential = CustomEasing(Easing.exp);
    EasingLibrary.Sinusoidal = CustomEasing(Easing.sin);
    EasingLibrary.Elastic = (bounciness) => CustomEasing(Easing.elastic(bounciness));
    EasingLibrary.Custom = (x1, y1, x2, y2) => CustomEasing(Easing.bezier(x1, y1, x2, y2));
})(EasingLibrary || (EasingLibrary = {}));
export default EasingLibrary;
