import execa from 'execa';
import { HeadlessBrowser } from './browser/Browser';
import { SymbolicateableError } from './error-handling/symbolicateable-error';
import { mimeContentType, mimeLookup } from './mime-types';
import * as perf from './perf';
export type { RenderMediaOnDownload } from './assets/download-and-map-assets-to-file';
export { Browser } from './browser';
export { BrowserExecutable } from './browser-executable';
export { BrowserLog } from './browser-log';
export type { HeadlessBrowser } from './browser/Browser';
export { Codec, CodecOrUndefined } from './codec';
export { CombineChunksOnProgress, CombineChunksOptions, combineChunks, } from './combine-chunks';
export { Crf } from './crf';
export { EnsureBrowserOptions, ensureBrowser } from './ensure-browser';
export { ErrorWithStackFrame } from './error-handling/handle-javascript-exception';
export { extractAudio } from './extract-audio';
export type { FfmpegOverrideFn } from './ffmpeg-override';
export { FileExtension } from './file-extensions';
export { FrameRange } from './frame-range';
export { GetCompositionsOptions, getCompositions } from './get-compositions';
export { SilentPart, getSilentParts } from './get-silent-parts';
export { VideoMetadata, getVideoMetadata } from './get-video-metadata';
export { ImageFormat, StillImageFormat, VideoImageFormat, validateSelectedPixelFormatAndImageFormatCombination, } from './image-format';
export type { LogLevel } from './log-level';
export { LogOptions } from './logger';
export { CancelSignal, makeCancelSignal } from './make-cancel-signal';
export { openBrowser } from './open-browser';
export type { ChromiumOptions } from './open-browser';
export { ChromeMode } from './options/chrome-mode';
export { ColorSpace } from './options/color-space';
export type { DeleteAfter } from './options/delete-after';
export { OpenGlRenderer } from './options/gl';
export { NumberOfGifLoops } from './options/number-of-gif-loops';
export { DownloadBrowserProgressFn, OnBrowserDownload, } from './options/on-browser-download';
export { AnyRemotionOption, RemotionOption, ToOptions } from './options/option';
export { X264Preset } from './options/x264-preset';
export { PixelFormat } from './pixel-format';
export { RemotionServer } from './prepare-server';
export { ProResProfile } from './prores-profile';
export { OnArtifact, RenderFramesOptions, renderFrames } from './render-frames';
export { InternalRenderMediaOptions, RenderMediaOnProgress, RenderMediaOptions, SlowFrame, StitchingState, renderMedia, } from './render-media';
export { RenderStillOptions, renderStill } from './render-still';
export { SelectCompositionOptions, selectComposition, } from './select-composition';
export { EmittedArtifact } from './serialize-artifact';
export { StitchFramesToVideoOptions, stitchFramesToVideo, } from './stitch-frames-to-video';
export { SymbolicatedStackFrame } from './symbolicate-stacktrace';
export { OnStartData, RenderFramesOutput } from './types';
export { validateOutputFilename } from './validate-output-filename';
export type { AudioCodec };
import type { AudioCodec } from './options/audio-codec';
import { toMegabytes } from './to-megabytes';
export declare const RenderInternals: {
    resolveConcurrency: (userPreference: number | string | null) => number;
    serveStatic: (path: string | null, options: {
        port: number | null;
        downloadMap: import("./assets/download-map").DownloadMap;
        remotionRoot: string;
        offthreadVideoThreads: number;
        logLevel: import("./log-level").LogLevel;
        indent: boolean;
        offthreadVideoCacheSizeInBytes: number | null;
        binariesDirectory: string | null;
        forceIPv4: boolean;
    }) => Promise<{
        port: number;
        close: () => Promise<void>;
        compositor: import("./compositor/compositor").Compositor;
    }>;
    validateEvenDimensionsWithCodec: ({ width, height, codec, scale, wantsImageSequence, indent, logLevel, }: {
        width: number;
        height: number;
        scale: number;
        codec: import("./codec").Codec;
        wantsImageSequence: boolean;
        indent: boolean;
        logLevel: import("./log-level").LogLevel;
    }) => {
        actualWidth: number;
        actualHeight: number;
    };
    getFileExtensionFromCodec: <T extends import("./codec").Codec>(codec: T, audioCodec: AudioCodec | null) => import("./file-extensions").FileExtension;
    tmpDir: (str: string) => string;
    deleteDirectory: (directory: string) => void;
    isServeUrl: (potentialUrl: string) => boolean;
    ensureOutputDirectory: (outputLocation: string) => void;
    getRealFrameRange: (durationInFrames: number, frameRange: import("./frame-range").FrameRange | null) => [number, number];
    validatePuppeteerTimeout: (timeoutInMilliseconds: unknown) => void;
    downloadFile: (options: {
        url: string;
        to: (contentDisposition: string | null, contentType: string | null) => string;
        onProgress: ((progress: {
            percent: number | null;
            downloaded: number;
            totalSize: number | null;
        }) => void) | undefined;
        logLevel: import("./log-level").LogLevel;
        indent: boolean;
    }, retries?: number, attempt?: number) => Promise<{
        sizeInBytes: number;
        to: string;
    }>;
    parseStack: (stack: string[]) => import("./parse-browser-error-stack").UnsymbolicatedStackFrame[];
    symbolicateError: (symbolicateableError: SymbolicateableError) => Promise<import("./error-handling/handle-javascript-exception").ErrorWithStackFrame>;
    SymbolicateableError: typeof SymbolicateableError;
    getFramesToRender: (frameRange: [number, number], everyNthFrame: number) => number[];
    getExtensionOfFilename: (filename: string | null) => string | null;
    getDesiredPort: ({ desiredPort, from, hostsToTry, to, }: {
        desiredPort: number | undefined;
        from: number;
        to: number;
        hostsToTry: string[];
    }) => Promise<{
        port: number;
        unlockPort: () => void;
    }>;
    isPathInside: (thePath: string, potentialParent: string) => boolean;
    execa: {
        (file: string, arguments?: readonly string[], options?: execa.Options): execa.ExecaChildProcess;
        (file: string, arguments?: readonly string[], options?: execa.Options<null>): execa.ExecaChildProcess<Buffer>;
        (file: string, options?: execa.Options): execa.ExecaChildProcess;
        (file: string, options?: execa.Options<null>): execa.ExecaChildProcess<Buffer>;
        sync(file: string, arguments?: readonly string[], options?: execa.SyncOptions): execa.ExecaSyncReturnValue;
        sync(file: string, arguments?: readonly string[], options?: execa.SyncOptions<null>): execa.ExecaSyncReturnValue<Buffer>;
        sync(file: string, options?: execa.SyncOptions): execa.ExecaSyncReturnValue;
        sync(file: string, options?: execa.SyncOptions<null>): execa.ExecaSyncReturnValue<Buffer>;
        command(command: string, options?: execa.Options): execa.ExecaChildProcess;
        command(command: string, options?: execa.Options<null>): execa.ExecaChildProcess<Buffer>;
        commandSync(command: string, options?: execa.SyncOptions): execa.ExecaSyncReturnValue;
        commandSync(command: string, options?: execa.SyncOptions<null>): execa.ExecaSyncReturnValue<Buffer>;
        node(scriptPath: string, arguments?: readonly string[], options?: execa.NodeOptions): execa.ExecaChildProcess;
        node(scriptPath: string, arguments?: readonly string[], options?: execa.Options<null>): execa.ExecaChildProcess<Buffer>;
        node(scriptPath: string, options?: execa.Options): execa.ExecaChildProcess;
        node(scriptPath: string, options?: execa.Options<null>): execa.ExecaChildProcess<Buffer>;
    };
    registerErrorSymbolicationLock: () => number;
    unlockErrorSymbolicationLock: (id: number) => void;
    canUseParallelEncoding: (codec: import("./codec").Codec) => boolean;
    mimeContentType: typeof mimeContentType;
    mimeLookup: typeof mimeLookup;
    validateConcurrency: ({ setting, value, checkIfValidForCurrentMachine, }: {
        value: unknown;
        setting: string;
        checkIfValidForCurrentMachine: boolean;
    }) => void;
    validPixelFormats: readonly ["yuv420p", "yuva420p", "yuv422p", "yuv444p", "yuv420p10le", "yuv422p10le", "yuv444p10le", "yuva444p10le"];
    DEFAULT_BROWSER: "chrome";
    validateFrameRange: (frameRange: import("./frame-range").FrameRange | null) => void;
    DEFAULT_OPENGL_RENDERER: "angle" | "swangle" | "egl" | "swiftshader" | "vulkan" | "angle-egl" | null;
    validateOpenGlRenderer: (option: unknown) => import("./options/gl").OpenGlRenderer | null;
    validCodecs: readonly ["h264", "h265", "vp8", "vp9", "mp3", "aac", "wav", "prores", "h264-mkv", "h264-ts", "gif"];
    DEFAULT_PIXEL_FORMAT: "yuv420p" | "yuva420p" | "yuv422p" | "yuv444p" | "yuv420p10le" | "yuv422p10le" | "yuv444p10le" | "yuva444p10le";
    validateJpegQuality: (q: unknown) => void;
    DEFAULT_TIMEOUT: number;
    DEFAULT_CODEC: "h264" | "h265" | "vp8" | "vp9" | "mp3" | "aac" | "wav" | "prores" | "h264-mkv" | "h264-ts" | "gif";
    logLevels: readonly ["trace", "verbose", "info", "warn", "error"];
    isEqualOrBelowLogLevel: (currentLevel: import("./log-level").LogLevel, level: import("./log-level").LogLevel) => boolean;
    isValidLogLevel: (level: string) => boolean;
    perf: typeof perf;
    convertToPositiveFrameIndex: ({ frame, durationInFrames, }: {
        frame: number;
        durationInFrames: number;
    }) => number;
    findRemotionRoot: () => string;
    validateBitrate: (bitrate: unknown, name: string) => void;
    getMinConcurrency: () => number;
    getMaxConcurrency: () => number;
    getDefaultAudioCodec: ({ codec, preferLossless, }: {
        codec: import("./codec").Codec;
        preferLossless: boolean;
    }) => AudioCodec | null;
    defaultFileExtensionMap: {
        h264: {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {
                mp3: {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
                aac: {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
                "pcm-16": {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
            };
        };
        h265: {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {
                aac: {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
                "pcm-16": {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
            };
        };
        vp8: {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {
                "pcm-16": {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
                opus: {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
            };
        };
        vp9: {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {
                "pcm-16": {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
                opus: {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
            };
        };
        mp3: {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {
                mp3: {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
                "pcm-16": {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
            };
        };
        aac: {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {
                aac: {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
                "pcm-16": {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
            };
        };
        wav: {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {
                "pcm-16": {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
            };
        };
        prores: {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {
                aac: {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
                "pcm-16": {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
            };
        };
        "h264-mkv": {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {
                mp3: {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
                "pcm-16": {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
            };
        };
        "h264-ts": {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {
                aac: {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
                "pcm-16": {
                    possible: import("./file-extensions").FileExtension[];
                    default: import("./file-extensions").FileExtension;
                };
            };
        };
        gif: {
            default: import("./file-extensions").FileExtension;
            forAudioCodec: {};
        };
    };
    supportedAudioCodecs: {
        readonly h264: readonly ["aac", "pcm-16", "mp3"];
        readonly 'h264-mkv': readonly ["pcm-16", "mp3"];
        readonly 'h264-ts': readonly ["pcm-16", "aac"];
        readonly aac: readonly ["aac", "pcm-16"];
        readonly avi: readonly [];
        readonly gif: readonly [];
        readonly h265: readonly ["aac", "pcm-16"];
        readonly mp3: readonly ["mp3", "pcm-16"];
        readonly prores: readonly ["aac", "pcm-16"];
        readonly vp8: readonly ["opus", "pcm-16"];
        readonly vp9: readonly ["opus", "pcm-16"];
        readonly wav: readonly ["pcm-16"];
    };
    makeFileExtensionMap: () => Record<string, ("h264" | "h265" | "vp8" | "vp9" | "mp3" | "aac" | "wav" | "prores" | "h264-mkv" | "h264-ts" | "gif")[]>;
    defaultCodecsForFileExtension: Record<import("./file-extensions").FileExtension, "h264" | "h265" | "vp8" | "vp9" | "mp3" | "aac" | "wav" | "prores" | "h264-mkv" | "h264-ts" | "gif">;
    getExecutablePath: ({ indent, logLevel, type, binariesDirectory, }: {
        type: "compositor" | "ffmpeg" | "ffprobe";
        indent: boolean;
        logLevel: import("./log-level").LogLevel;
        binariesDirectory: string | null;
    }) => string;
    callFf: ({ args, bin, indent, logLevel, options, binariesDirectory, cancelSignal, }: {
        bin: "ffmpeg" | "ffprobe";
        args: (string | null)[];
        indent: boolean;
        logLevel: import("./log-level").LogLevel;
        binariesDirectory: string | null;
        cancelSignal: import("./make-cancel-signal").CancelSignal | undefined;
        options?: execa.Options<string>;
    }) => execa.ExecaChildProcess<string>;
    validStillImageFormats: readonly ["png", "jpeg", "pdf", "webp"];
    validVideoImageFormats: readonly ["png", "jpeg", "none"];
    DEFAULT_STILL_IMAGE_FORMAT: "jpeg" | "png" | "webp" | "pdf";
    DEFAULT_VIDEO_IMAGE_FORMAT: "jpeg" | "png" | "none";
    DEFAULT_JPEG_QUALITY: number;
    chalk: {
        enabled: () => boolean;
        visible: boolean;
        styles: Record<string, {
            codes: [number, number];
            name: string;
            wrap?: (input: string, newline: boolean) => string;
        }>;
        keys: Record<string, string[]>;
        alias?: (name: string, col: string) => void;
    } & {
        reset: (str: string) => string;
        bold: (str: string) => string;
        dim: (str: string) => string;
        italic: (str: string) => string;
        underline: (str: string) => string;
        inverse: (str: string) => string;
        hidden: (str: string) => string;
        strikethrough: (str: string) => string;
        black: (str: string) => string;
        red: (str: string) => string;
        green: (str: string) => string;
        yellow: (str: string) => string;
        blue: (str: string) => string;
        magenta: (str: string) => string;
        cyan: (str: string) => string;
        white: (str: string) => string;
        gray: (str: string) => string;
        bgBlack: (str: string) => string;
        bgRed: (str: string) => string;
        bgGreen: (str: string) => string;
        bgYellow: (str: string) => string;
        bgBlue: (str: string) => string;
        bgMagenta: (str: string) => string;
        bgWhite: (str: string) => string;
        blackBright: (str: string) => string;
        redBright: (str: string) => string;
        greenBright: (str: string) => string;
        yellowBright: (str: string) => string;
        blueBright: (str: string) => string;
        magentaBright: (str: string) => string;
        whiteBright: (str: string) => string;
        bgBlackBright: (str: string) => string;
        bgRedBright: (str: string) => string;
        bgGreenBright: (str: string) => string;
        bgYellowBright: (str: string) => string;
        bgBlueBright: (str: string) => string;
        bgMagentaBright: (str: string) => string;
        bgWhiteBright: (str: string) => string;
    };
    Log: {
        trace: (options: import("./logger").LogOptions & {
            tag?: string;
        }, message?: any, ...optionalParams: any[]) => boolean | void;
        verbose: (options: import("./logger").LogOptions & {
            tag?: string;
        }, message?: any, ...optionalParams: any[]) => boolean | void;
        info: (options: import("./logger").LogOptions, message?: any, ...optionalParams: any[]) => boolean | void;
        warn: (options: import("./logger").LogOptions, message?: any, ...optionalParams: any[]) => boolean | void;
        error: (options: import("./logger").LogOptions & {
            tag?: string;
        }, message?: any, ...optionalParams: any[]) => boolean | void;
    };
    INDENT_TOKEN: string;
    isColorSupported: () => boolean;
    HeadlessBrowser: typeof HeadlessBrowser;
    prepareServer: ({ webpackConfigOrServeUrl, port, remotionRoot, offthreadVideoThreads, logLevel, indent, offthreadVideoCacheSizeInBytes, binariesDirectory, forceIPv4, }: {
        webpackConfigOrServeUrl: string;
        port: number | null;
        remotionRoot: string;
        offthreadVideoThreads: number;
        logLevel: import("./log-level").LogLevel;
        indent: boolean;
        offthreadVideoCacheSizeInBytes: number | null;
        binariesDirectory: string | null;
        forceIPv4: boolean;
    }) => Promise<import("./prepare-server").RemotionServer>;
    makeOrReuseServer: (server: import("./prepare-server").RemotionServer | undefined, config: {
        webpackConfigOrServeUrl: string;
        port: number | null;
        remotionRoot: string;
        offthreadVideoThreads: number;
        logLevel: import("./log-level").LogLevel;
        indent: boolean;
        offthreadVideoCacheSizeInBytes: number | null;
        binariesDirectory: string | null;
        forceIPv4: boolean;
    }, { onDownload, }: {
        onDownload: import("./assets/download-and-map-assets-to-file").RenderMediaOnDownload | null;
    }) => Promise<{
        server: import("./prepare-server").RemotionServer;
        cleanupServer: (force: boolean) => Promise<unknown>;
    }>;
    internalRenderStill: (options: {
        composition: import("remotion").VideoConfig;
        output: string | null;
        frame: number;
        serializedInputPropsWithCustomSchema: string;
        serializedResolvedPropsWithCustomSchema: string;
        imageFormat: import("./image-format").StillImageFormat;
        jpegQuality: number;
        puppeteerInstance: HeadlessBrowser | null;
        envVariables: Record<string, string>;
        overwrite: boolean;
        browserExecutable: import("./browser-executable").BrowserExecutable;
        onBrowserLog: null | ((log: import("./browser-log").BrowserLog) => void);
        chromiumOptions: import("./open-browser").ChromiumOptions;
        scale: number;
        onDownload: import("./assets/download-and-map-assets-to-file").RenderMediaOnDownload | null;
        cancelSignal: import("./make-cancel-signal").CancelSignal | null;
        indent: boolean;
        server: import("./prepare-server").RemotionServer | undefined;
        serveUrl: string;
        port: number | null;
        onArtifact: import("./render-frames").OnArtifact | null;
    } & import("./options/option").ToOptions<{
        readonly offthreadVideoCacheSizeInBytes: {
            name: string;
            cliFlag: "offthreadvideo-cache-size-in-bytes";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoCacheSizeInBytes";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly offthreadVideoThreads: {
            name: string;
            cliFlag: "offthreadvideo-video-threads";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoThreads";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly jpegQuality: {
            name: string;
            cliFlag: "jpeg-quality";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: number;
            setConfig: (q: number | undefined) => void;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
        };
        readonly logLevel: {
            cliFlag: "log";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./log-level").LogLevel;
                source: string;
            };
            setConfig: (newLogLevel: import("./log-level").LogLevel) => void;
            type: import("./log-level").LogLevel;
        };
        readonly timeoutInMilliseconds: {
            name: string;
            cliFlag: "timeout";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "timeoutInMilliseconds";
            docLink: string;
            type: number;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
            setConfig: (value: number) => void;
        };
        readonly binariesDirectory: {
            name: string;
            cliFlag: "binaries-directory";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "binariesDirectory";
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string | null;
            };
            setConfig: (value: string | null) => void;
        };
        readonly onBrowserDownload: {
            name: string;
            cliFlag: "on-browser-download";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "onBrowserDownload";
            docLink: string;
            type: import("./options/on-browser-download").OnBrowserDownload;
            getValue: () => never;
            setConfig: () => never;
        };
        readonly chromeMode: {
            cliFlag: "chrome-mode";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./options/chrome-mode").ChromeMode;
                source: string;
            };
            setConfig: (newChromeMode: import("./options/chrome-mode").ChromeMode) => void;
            type: import("./options/chrome-mode").ChromeMode;
        };
    }>) => Promise<{
        buffer: Buffer | null;
    }>;
    internalOpenBrowser: ({ browser, browserExecutable, chromiumOptions, forceDeviceScaleFactor, indent, viewport, logLevel, onBrowserDownload, chromeMode, }: {
        browserExecutable: string | null;
        chromiumOptions: import("./open-browser").ChromiumOptions;
        forceDeviceScaleFactor: number | undefined;
        viewport: import("./browser/PuppeteerViewport").Viewport | null;
        indent: boolean;
        browser: import("./browser").Browser;
    } & import("./options/option").ToOptions<{
        readonly logLevel: {
            cliFlag: "log";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./log-level").LogLevel;
                source: string;
            };
            setConfig: (newLogLevel: import("./log-level").LogLevel) => void;
            type: import("./log-level").LogLevel;
        };
        readonly onBrowserDownload: {
            name: string;
            cliFlag: "on-browser-download";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "onBrowserDownload";
            docLink: string;
            type: import("./options/on-browser-download").OnBrowserDownload;
            getValue: () => never;
            setConfig: () => never;
        };
        readonly chromeMode: {
            cliFlag: "chrome-mode";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./options/chrome-mode").ChromeMode;
                source: string;
            };
            setConfig: (newChromeMode: import("./options/chrome-mode").ChromeMode) => void;
            type: import("./options/chrome-mode").ChromeMode;
        };
    }>) => Promise<HeadlessBrowser>;
    internalSelectComposition: (options: {
        serializedInputPropsWithCustomSchema: string;
        envVariables: Record<string, string>;
        puppeteerInstance: HeadlessBrowser | undefined;
        onBrowserLog: null | ((log: import("./browser-log").BrowserLog) => void);
        browserExecutable: import("./browser-executable").BrowserExecutable | null;
        chromiumOptions: import("./open-browser").ChromiumOptions;
        port: number | null;
        indent: boolean;
        server: import("./prepare-server").RemotionServer | undefined;
        serveUrl: string;
        id: string;
        onServeUrlVisited: () => void;
    } & import("./options/option").ToOptions<{
        readonly offthreadVideoCacheSizeInBytes: {
            name: string;
            cliFlag: "offthreadvideo-cache-size-in-bytes";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoCacheSizeInBytes";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly offthreadVideoThreads: {
            name: string;
            cliFlag: "offthreadvideo-video-threads";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoThreads";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly logLevel: {
            cliFlag: "log";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./log-level").LogLevel;
                source: string;
            };
            setConfig: (newLogLevel: import("./log-level").LogLevel) => void;
            type: import("./log-level").LogLevel;
        };
        readonly timeoutInMilliseconds: {
            name: string;
            cliFlag: "timeout";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "timeoutInMilliseconds";
            docLink: string;
            type: number;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
            setConfig: (value: number) => void;
        };
        readonly binariesDirectory: {
            name: string;
            cliFlag: "binaries-directory";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "binariesDirectory";
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string | null;
            };
            setConfig: (value: string | null) => void;
        };
        readonly onBrowserDownload: {
            name: string;
            cliFlag: "on-browser-download";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "onBrowserDownload";
            docLink: string;
            type: import("./options/on-browser-download").OnBrowserDownload;
            getValue: () => never;
            setConfig: () => never;
        };
        readonly chromeMode: {
            cliFlag: "chrome-mode";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./options/chrome-mode").ChromeMode;
                source: string;
            };
            setConfig: (newChromeMode: import("./options/chrome-mode").ChromeMode) => void;
            type: import("./options/chrome-mode").ChromeMode;
        };
    }>) => Promise<{
        metadata: import("remotion").VideoConfig;
        propsSize: number;
    }>;
    internalGetCompositions: (args_0: {
        serializedInputPropsWithCustomSchema: string;
        envVariables: Record<string, string>;
        puppeteerInstance: HeadlessBrowser | undefined;
        onBrowserLog: null | ((log: import("./browser-log").BrowserLog) => void);
        browserExecutable: import("./browser-executable").BrowserExecutable | null;
        chromiumOptions: import("./open-browser").ChromiumOptions;
        port: number | null;
        server: import("./prepare-server").RemotionServer | undefined;
        indent: boolean;
        serveUrlOrWebpackUrl: string;
    } & import("./options/option").ToOptions<{
        readonly offthreadVideoCacheSizeInBytes: {
            name: string;
            cliFlag: "offthreadvideo-cache-size-in-bytes";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoCacheSizeInBytes";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly offthreadVideoThreads: {
            name: string;
            cliFlag: "offthreadvideo-video-threads";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoThreads";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly logLevel: {
            cliFlag: "log";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./log-level").LogLevel;
                source: string;
            };
            setConfig: (newLogLevel: import("./log-level").LogLevel) => void;
            type: import("./log-level").LogLevel;
        };
        readonly timeoutInMilliseconds: {
            name: string;
            cliFlag: "timeout";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "timeoutInMilliseconds";
            docLink: string;
            type: number;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
            setConfig: (value: number) => void;
        };
        readonly binariesDirectory: {
            name: string;
            cliFlag: "binaries-directory";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "binariesDirectory";
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string | null;
            };
            setConfig: (value: string | null) => void;
        };
        readonly onBrowserDownload: {
            name: string;
            cliFlag: "on-browser-download";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "onBrowserDownload";
            docLink: string;
            type: import("./options/on-browser-download").OnBrowserDownload;
            getValue: () => never;
            setConfig: () => never;
        };
        readonly chromeMode: {
            cliFlag: "chrome-mode";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./options/chrome-mode").ChromeMode;
                source: string;
            };
            setConfig: (newChromeMode: import("./options/chrome-mode").ChromeMode) => void;
            type: import("./options/chrome-mode").ChromeMode;
        };
    }>) => Promise<import("remotion").VideoConfig[]>;
    internalRenderFrames: (args_0: {
        onStart: null | ((data: import("./types").OnStartData) => void);
        onFrameUpdate: null | ((framesRendered: number, frameIndex: number, timeToRenderInMilliseconds: number) => void);
        outputDir: string | null;
        envVariables: Record<string, string>;
        imageFormat: import("./image-format").VideoImageFormat;
        jpegQuality: number;
        frameRange: import("./frame-range").FrameRange | null;
        everyNthFrame: number;
        puppeteerInstance: HeadlessBrowser | undefined;
        browserExecutable: import("./browser-executable").BrowserExecutable | null;
        onBrowserLog: null | ((log: import("./browser-log").BrowserLog) => void);
        onFrameBuffer: null | ((buffer: Buffer, frame: number) => void);
        onDownload: import("./assets/download-and-map-assets-to-file").RenderMediaOnDownload | null;
        chromiumOptions: import("./open-browser").ChromiumOptions;
        scale: number;
        port: number | null;
        cancelSignal: import("./make-cancel-signal").CancelSignal | undefined;
        composition: Omit<import("remotion").VideoConfig, "props" | "defaultProps">;
        indent: boolean;
        server: import("./prepare-server").RemotionServer | undefined;
        muted: boolean;
        concurrency: number | string | null;
        webpackBundleOrServeUrl: string;
        serializedInputPropsWithCustomSchema: string;
        serializedResolvedPropsWithCustomSchema: string;
        parallelEncodingEnabled: boolean;
        compositionStart: number;
        onArtifact: import("./render-frames").OnArtifact | null;
    } & import("./options/option").ToOptions<{
        readonly forSeamlessAacConcatenation: {
            name: string;
            cliFlag: "for-seamless-aac-concatenation";
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: true;
            } | {
                source: string;
                value: false;
            };
            setConfig: (value: boolean) => void;
            ssrName: string;
            type: boolean;
        };
        readonly offthreadVideoCacheSizeInBytes: {
            name: string;
            cliFlag: "offthreadvideo-cache-size-in-bytes";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoCacheSizeInBytes";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly offthreadVideoThreads: {
            name: string;
            cliFlag: "offthreadvideo-video-threads";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "offthreadVideoThreads";
            docLink: string;
            type: number | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            } | {
                source: string;
                value: null;
            };
            setConfig: (size: number | null) => void;
        };
        readonly jpegQuality: {
            name: string;
            cliFlag: "jpeg-quality";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: string;
            docLink: string;
            type: number;
            setConfig: (q: number | undefined) => void;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
        };
        readonly logLevel: {
            cliFlag: "log";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./log-level").LogLevel;
                source: string;
            };
            setConfig: (newLogLevel: import("./log-level").LogLevel) => void;
            type: import("./log-level").LogLevel;
        };
        readonly timeoutInMilliseconds: {
            name: string;
            cliFlag: "timeout";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "timeoutInMilliseconds";
            docLink: string;
            type: number;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: number;
            };
            setConfig: (value: number) => void;
        };
        readonly binariesDirectory: {
            name: string;
            cliFlag: "binaries-directory";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "binariesDirectory";
            docLink: string;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                source: string;
                value: string | null;
            };
            setConfig: (value: string | null) => void;
        };
        readonly onBrowserDownload: {
            name: string;
            cliFlag: "on-browser-download";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "onBrowserDownload";
            docLink: string;
            type: import("./options/on-browser-download").OnBrowserDownload;
            getValue: () => never;
            setConfig: () => never;
        };
        readonly chromeMode: {
            cliFlag: "chrome-mode";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./options/chrome-mode").ChromeMode;
                source: string;
            };
            setConfig: (newChromeMode: import("./options/chrome-mode").ChromeMode) => void;
            type: import("./options/chrome-mode").ChromeMode;
        };
        readonly imageSequencePattern: {
            name: string;
            cliFlag: "image-sequence-pattern";
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: null;
            type: string | null;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: string;
                source: string;
            };
            setConfig: (pattern: string | null) => void;
        };
    }>) => Promise<import("./types").RenderFramesOutput>;
    internalRenderMedia: (args_0: import("./render-media").InternalRenderMediaOptions) => Promise<{
        buffer: Buffer | null;
        slowestFrames: import("./render-media").SlowFrame[];
    }>;
    validOpenGlRenderers: readonly ["swangle", "angle", "egl", "swiftshader", "vulkan", "angle-egl"];
    isIpV6Supported: (flattened: import("os").NetworkInterfaceInfo[]) => boolean;
    getChromiumGpuInformation: ({ browserExecutable, indent, logLevel, chromiumOptions, timeoutInMilliseconds, onBrowserDownload, chromeMode, }: {
        browserExecutable: import("./browser-executable").BrowserExecutable;
        indent: boolean;
        logLevel: import("./log-level").LogLevel;
        chromiumOptions: import("./open-browser").ChromiumOptions;
        timeoutInMilliseconds: number;
        onBrowserDownload: import("./options/on-browser-download").OnBrowserDownload;
        chromeMode: import("./options/chrome-mode").ChromeMode;
    }) => Promise<{
        feature: string;
        status: string;
    }[]>;
    getPortConfig: (preferIpv4: boolean) => {
        host: string;
        hostsToTry: string[];
    };
    makeDownloadMap: () => import("./assets/download-map").DownloadMap;
    getExtensionFromAudioCodec: (audioCodec: AudioCodec) => "mp3" | "aac" | "wav" | "opus";
    makeFileExecutableIfItIsNot: (path: string) => void;
    resolveAudioCodec: ({ codec, setting, preferLossless, separateAudioTo, }: {
        setting: AudioCodec | null;
        codec: import("./codec").Codec;
        preferLossless: boolean;
        separateAudioTo: string | null;
    }) => "mp3" | "aac" | "pcm-16" | "opus" | null;
    getShouldRenderAudio: ({ codec, assetsInfo, enforceAudioTrack, muted, }: {
        codec: import("./codec").Codec;
        assetsInfo: import("./assets/download-map").RenderAssetInfo | null;
        enforceAudioTrack: boolean;
        muted: boolean;
    }) => "yes" | "maybe" | "no";
    codecSupportsMedia: (codec: import("./codec").Codec) => {
        video: boolean;
        audio: boolean;
    };
    toMegabytes: typeof toMegabytes;
    internalEnsureBrowser: (options: {
        browserExecutable: import("./browser-executable").BrowserExecutable;
        indent: boolean;
        chromeMode: import("./options/chrome-mode").ChromeMode;
    } & import("./options/option").ToOptions<{
        readonly logLevel: {
            cliFlag: "log";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./log-level").LogLevel;
                source: string;
            };
            setConfig: (newLogLevel: import("./log-level").LogLevel) => void;
            type: import("./log-level").LogLevel;
        };
        readonly onBrowserDownload: {
            name: string;
            cliFlag: "on-browser-download";
            description: () => import("react/jsx-runtime").JSX.Element;
            ssrName: "onBrowserDownload";
            docLink: string;
            type: import("./options/on-browser-download").OnBrowserDownload;
            getValue: () => never;
            setConfig: () => never;
        };
        readonly chromeMode: {
            cliFlag: "chrome-mode";
            name: string;
            ssrName: string;
            description: () => import("react/jsx-runtime").JSX.Element;
            docLink: string;
            getValue: ({ commandLine }: {
                commandLine: Record<string, unknown>;
            }) => {
                value: import("./options/chrome-mode").ChromeMode;
                source: string;
            };
            setConfig: (newChromeMode: import("./options/chrome-mode").ChromeMode) => void;
            type: import("./options/chrome-mode").ChromeMode;
        };
    }>) => Promise<import("./ensure-browser").BrowserStatus>;
    printUsefulErrorMessage: (err: Error, logLevel: import("./log-level").LogLevel, indent: boolean) => void;
    DEFAULT_RENDER_FRAMES_OFFTHREAD_VIDEO_THREADS: number;
    canConcatVideoSeamlessly: (codec: import("./codec").Codec) => codec is "h264";
    canConcatAudioSeamlessly: (audioCodec: AudioCodec | null, chunkDurationInFrames: number) => boolean;
    internalCombineChunks: ({ outputLocation: output, onProgress, codec, fps, numberOfGifLoops, audioBitrate, indent, logLevel, binariesDirectory, cancelSignal, metadata, audioFiles, videoFiles, framesPerChunk, audioCodec, preferLossless, everyNthFrame, frameRange, compositionDurationInFrames, }: ({
        outputLocation: string;
        audioFiles: string[];
        codec: import("./codec").Codec;
        videoFiles: string[];
        fps: number;
        framesPerChunk: number;
        preferLossless: boolean;
        compositionDurationInFrames: number;
    } & {
        onProgress: import("./combine-chunks").CombineChunksOnProgress;
        audioBitrate: string | null;
        numberOfGifLoops: number | null;
        logLevel: import("./log-level").LogLevel;
        binariesDirectory: string | null;
        audioCodec: AudioCodec | null;
        cancelSignal: import("./make-cancel-signal").CancelSignal | undefined;
        metadata: Record<string, string> | null;
        frameRange: import("./frame-range").FrameRange | null;
        everyNthFrame: number;
    }) & {
        indent: boolean;
    }) => Promise<void>;
};
