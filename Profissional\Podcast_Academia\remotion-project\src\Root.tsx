import React from "react";
import { Composition } from "remotion";
import { InstagramReel } from "./InstagramReel";
import { InstagramStory } from "./InstagramStory";
import { InstagramPost } from "./InstagramPost";
import { ViralInstagramReel } from "./ViralInstagramReel";
import { ViralInstagramReelAdvanced } from "./ViralInstagramReelAdvanced";
import { ViralReelProfessional } from "./ViralReelProfessional";

export const RemotionRoot: React.FC = () => {
  return (
    <>
      <Composition
        id="InstagramReel"
        component={InstagramReel}
        durationInFrames={1800} // 60 segundos a 30fps
        fps={30}
        width={1080}
        height={1920}
        defaultProps={{
          videoPath: "Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4",
          startTime: 0,
          duration: 60,
          title: "Como construir seu primeiro Agente IA",
          subtitle: "Do ZERO em 90 dias",
          author: "<PERSON>",
          colors: {
            primary: "#FF6B35",
            secondary: "#2E86AB",
            accent: "#A23B72",
            background: "#F18F01",
            text: "#FFFFFF"
          }
        }}
      />
      <Composition
        id="InstagramStory"
        component={InstagramStory}
        durationInFrames={450} // 15 segundos a 30fps
        fps={30}
        width={1080}
        height={1920}
        defaultProps={{
          videoPath: "Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4",
          startTime: 0,
          duration: 15,
          title: "Agente IA em 90 dias",
          author: "Sami",
          colors: {
            primary: "#1f2937",      // Cinza escuro profissional
            secondary: "#6b7280",    // Cinza médio
            accent: "#374151",       // Cinza neutro
            background: "#f9fafb",   // Branco suave
            text: "#111827"          // Preto para texto
          }
        }}
      />
      <Composition
        id="InstagramPost"
        component={InstagramPost}
        durationInFrames={1800} // 60 segundos a 30fps
        fps={30}
        width={1080}
        height={1080}
        defaultProps={{
          videoPath: "Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4",
          startTime: 0,
          duration: 60,
          title: "Como construir seu primeiro Agente IA",
          author: "Sami",
          colors: {
            primary: "#FF6B35",
            secondary: "#2E86AB",
            accent: "#A23B72",
            background: "#F18F01",
            text: "#FFFFFF"
          }
        }}
      />

      {/* Composição Viral Profissional */}
      <Composition
        id="ViralInstagramReel"
        component={ViralInstagramReel}
        durationInFrames={1350} // 45 segundos a 30fps
        fps={30}
        width={1080}
        height={1920}
        defaultProps={{
          videoPath: "Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4",
          audioPath: "audio_viral_segment.mp3",
          startTime: 720, // 12 minutos - momento do primeiro cliente
          duration: 45,
          title: "Como conseguir o primeiro cliente",
          subtitle: "E fechar um contrato de R$ 25 mil",
          hook: "💰 R$ 25 MIL em 90 dias",
          cta: "Descubra o método",
          author: "Academia"
        }}
      />

      {/* Composição Viral AVANÇADA com Imagens Dinâmicas */}
      <Composition
        id="ViralInstagramReelAdvanced"
        component={ViralInstagramReelAdvanced}
        durationInFrames={1350} // 45 segundos a 30fps
        fps={30}
        width={1080}
        height={1920}
        defaultProps={{
          videoPath: "Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4",
          audioPath: "audio_viral_segment.mp3",
          startTime: 720, // 12 minutos - momento do primeiro cliente
          duration: 45,
          title: "Como conseguir o primeiro cliente",
          subtitle: "E fechar um contrato de R$ 25 mil",
          hook: "💰 R$ 25 MIL em 90 dias",
          cta: "Descubra o método",
          author: "Academia"
        }}
      />

      {/* Composição VIRAL PROFISSIONAL - Padrões Reais */}
      <Composition
        id="ViralReelProfessional"
        component={ViralReelProfessional}
        durationInFrames={1350} // 45 segundos a 30fps
        fps={30}
        width={1080}
        height={1920}
        defaultProps={{
          videoPath: "Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4",
          audioPath: "audio_viral_segment.mp3",
          startTime: 729, // 12:09 - momento exato do primeiro cliente
          duration: 45,
        }}
      />
    </>
  );
};
