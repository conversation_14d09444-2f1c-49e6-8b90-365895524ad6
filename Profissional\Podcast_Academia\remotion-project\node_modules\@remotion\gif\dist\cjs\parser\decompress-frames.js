"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.decompressFrames = void 0;
const gifuct_1 = require("../gifuct");
const decompressFrames = (parsedGif) => {
    return parsedGif.frames
        .filter((f) => {
        return 'image' in f;
    })
        .map((f) => {
        return (0, gifuct_1.decompressFrame)(f, parsedGif.gct);
    })
        .filter(Boolean)
        .map((f) => f);
};
exports.decompressFrames = decompressFrames;
