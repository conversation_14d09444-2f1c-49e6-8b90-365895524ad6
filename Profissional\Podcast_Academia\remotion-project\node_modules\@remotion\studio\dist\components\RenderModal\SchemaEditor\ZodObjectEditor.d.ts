import React from 'react';
import type { z } from 'zod';
import type { UpdaterFunction } from './ZodSwitch';
import type { JSONPath } from './zod-types';
export type ObjectDiscrimatedUnionReplacement = {
    discriminator: string;
    markup: React.ReactNode;
};
export declare const ZodObjectEditor: React.FC<{
    readonly schema: z.ZodTypeAny;
    readonly jsonPath: JSONPath;
    readonly unsavedValue: Record<string, unknown>;
    readonly savedValue: Record<string, unknown>;
    readonly setValue: UpdaterFunction<Record<string, unknown>>;
    readonly onSave: UpdaterFunction<Record<string, unknown>>;
    readonly showSaveButton: boolean;
    readonly onRemove: null | (() => void);
    readonly saving: boolean;
    readonly saveDisabledByParent: boolean;
    readonly mayPad: boolean;
    readonly discriminatedUnionReplacement: ObjectDiscrimatedUnionReplacement | null;
}>;
