export interface Options<KeyType, ValueType> {
    /**
    The maximum number of milliseconds an item should remain in the cache.

    @default Infinity

    By default, `maxAge` will be `Infinity`, which means that items will never expire.
    Lazy expiration upon the next write or read call.

    Individual expiration of an item can be specified by the `set(key, value, maxAge)` method.
    */
    readonly maxAge?: number;
    /**
    The maximum number of items before evicting the least recently used items.
    */
    readonly maxSize: number;
    /**
    Called right before an item is evicted from the cache.

    Useful for side effects or for items like object URLs that need explicit cleanup (`revokeObjectURL`).
    */
    onEviction?: (key: KeyType, value: ValueType) => void;
}
export declare class QuickLRU<KeyType, ValueType> implements Iterable<[KeyType, ValueType]> {
    /**
    The maximum number of milliseconds an item should remain in the cache.

    @default Infinity

    By default, `maxAge` will be `Infinity`, which means that items will never expire.
    Lazy expiration upon the next write or read call.

    Individual expiration of an item can be specified by the `set(key, value, maxAge)` method.
    */
    maxAge: number;
    /**
    The maximum number of items before evicting the least recently used items.
    */
    maxSize: number;
    /**
    Called right before an item is evicted from the cache.

    Useful for side effects or for items like object URLs that need explicit cleanup (`revokeObjectURL`).
    */
    onEviction?: (key: KeyType, value: ValueType) => void;
    private _size;
    private cache;
    private oldCache;
    /**
    Simple ["Least Recently Used" (LRU) cache](https://en.m.wikipedia.org/wiki/Cache_replacement_policies#Least_Recently_Used_.28LRU.29).

    The instance is an [`Iterable`](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Iteration_protocols) of `[key, value]` pairs so you can use it directly in a [`for…of`](https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Statements/for...of) loop.

    @example
    ```
    import { QuickLRU } from 'quick-lru-ts';

    const lru = new QuickLRU({maxSize: 1000});

    lru.set('🦄', '🌈');

    lru.has('🦄');
    //=> true

    lru.get('🦄');
    //=> '🌈'
    ```
    */
    constructor(options: Options<KeyType, ValueType>);
    private _emitEvictions;
    private _deleteIfExpired;
    private _getOrDeleteIfExpired;
    private _getItemValue;
    private _peek;
    private _set;
    private _moveToRecent;
    private _entriesAscending;
    /**
    Get an item.

    @returns The stored item or `undefined`.
    */
    get(key: KeyType): ValueType | undefined;
    set(key: KeyType, value: ValueType, { maxAge }?: {
        maxAge?: number;
    }): void;
    has(key: KeyType): boolean;
    peek(key: KeyType): ValueType | undefined;
    delete(key: KeyType): boolean;
    clear(): void;
    resize(maxSize: number): void;
    keys(): Generator<KeyType, void, unknown>;
    values(): Generator<ValueType, void, unknown>;
    [Symbol.iterator](): IterableIterator<[KeyType, ValueType]>;
    /**
    Iterable for all entries, starting with the newest (descending in recency).
    */
    entriesDescending(): IterableIterator<[KeyType, ValueType]>;
    entriesAscending(): Generator<(KeyType | ValueType)[], void, unknown>;
    get size(): number;
}
