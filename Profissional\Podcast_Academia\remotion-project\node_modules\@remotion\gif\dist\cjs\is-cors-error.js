"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isCorsError = void 0;
const isCorsError = (error) => {
    return (
    // Chrome
    error.message.includes('Failed to fetch') ||
        // Safari
        error.message.includes('Load failed') ||
        // Firefox
        error.message.includes('NetworkError when attempting to fetch resource'));
};
exports.isCorsError = isCorsError;
