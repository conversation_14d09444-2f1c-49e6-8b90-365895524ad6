# 🚀 Script de Instalação Rápida - Sistema de Edição Profissional para Instagram

Write-Host "🎬 SISTEMA DE EDIÇÃO PROFISSIONAL PARA INSTAGRAM" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan
Write-Host ""

# Verificar se Node.js está instalado
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js encontrado: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js não encontrado. Instale o Node.js primeiro." -ForegroundColor Red
    exit 1
}

# Verificar se npm está instalado
try {
    $npmVersion = npm --version
    Write-Host "✅ npm encontrado: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm não encontrado. Instale o npm primeiro." -ForegroundColor Red
    exit 1
}

# Verificar se FFmpeg está instalado
try {
    $ffmpegVersion = ffmpeg -version 2>&1 | Select-String -Pattern "ffmpeg version" | Select-Object -First 1
    Write-Host "✅ FFmpeg encontrado: $ffmpegVersion" -ForegroundColor Green
} catch {
    Write-Host "⚠️  FFmpeg não encontrado. Tentando instalar..." -ForegroundColor Yellow
    
    # Tentar instalar com chocolatey
    try {
        choco install ffmpeg -y
        Write-Host "✅ FFmpeg instalado com chocolatey" -ForegroundColor Green
    } catch {
        # Tentar instalar com winget
        try {
            winget install FFmpeg
            Write-Host "✅ FFmpeg instalado com winget" -ForegroundColor Green
        } catch {
            Write-Host "❌ Erro ao instalar FFmpeg. Instale manualmente:" -ForegroundColor Red
            Write-Host "   https://ffmpeg.org/download.html" -ForegroundColor Red
            exit 1
        }
    }
}

Write-Host ""
Write-Host "📦 Instalando dependências..." -ForegroundColor Yellow
Write-Host ""

# Instalar dependências do npm
try {
    npm install
    Write-Host "✅ Dependências instaladas com sucesso" -ForegroundColor Green
} catch {
    Write-Host "❌ Erro ao instalar dependências" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🔧 Instalando Remotion CLI globalmente..." -ForegroundColor Yellow
Write-Host ""

# Instalar Remotion CLI
try {
    npm install -g @remotion/cli
    Write-Host "✅ Remotion CLI instalado com sucesso" -ForegroundColor Green
} catch {
    Write-Host "❌ Erro ao instalar Remotion CLI" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎯 Configuração concluída!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 PRÓXIMOS PASSOS:" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. Para testar a visualização:" -ForegroundColor White
Write-Host "   npm run dev" -ForegroundColor Yellow
Write-Host ""
Write-Host "2. Para renderizar um vídeo de teste:" -ForegroundColor White
Write-Host "   npm run render:reels" -ForegroundColor Yellow
Write-Host ""
Write-Host "3. Para renderizar todos os vídeos:" -ForegroundColor White
Write-Host "   npm run render:all" -ForegroundColor Yellow
Write-Host ""
Write-Host "4. Para editar as configurações:" -ForegroundColor White
Write-Host "   - Cores e temas: src/index.ts" -ForegroundColor Yellow
Write-Host "   - Vídeos: scripts/render-all.js" -ForegroundColor Yellow
Write-Host "   - Legendas: src/components/DynamicSubtitles.tsx" -ForegroundColor Yellow
Write-Host ""
Write-Host "📁 Os vídeos serão salvos na pasta: output/" -ForegroundColor Cyan
Write-Host ""
Write-Host "🎉 Sistema pronto para uso!" -ForegroundColor Green
Write-Host ""
Write-Host "💡 Dica: Coloque seus vídeos de podcast na pasta principal" -ForegroundColor Magenta
Write-Host "   e execute 'npm run render:all' para gerar conteúdo viral!" -ForegroundColor Magenta

# Verificar se o diretório output existe
$outputDir = "output"
if (!(Test-Path -Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir
    Write-Host "📁 Diretório output criado" -ForegroundColor Green
}

Write-Host ""
Write-Host "🚀 Executando teste inicial..." -ForegroundColor Yellow

# Executar teste inicial
try {
    npm run dev --help
    Write-Host "✅ Sistema funcionando corretamente!" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Verifique se todas as dependências foram instaladas corretamente" -ForegroundColor Yellow
}
