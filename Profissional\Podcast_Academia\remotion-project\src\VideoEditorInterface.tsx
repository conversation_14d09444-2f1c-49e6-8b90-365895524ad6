import React, { useState, useEffect } from "react";
import {
  AbsoluteFill,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  spring,
  Easing,
} from "remotion";

interface VideoEditorInterfaceProps {
  onSettingsChange?: (settings: VideoSettings) => void;
}

interface VideoSettings {
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
  timing: {
    introLength: number;
    outroLength: number;
    subtitleDelay: number;
  };
  effects: {
    enableParticles: boolean;
    enableGlow: boolean;
    enableShimmer: boolean;
    animationSpeed: number;
  };
  audio: {
    volume: number;
    fadeIn: number;
    fadeOut: number;
  };
}

const defaultSettings: VideoSettings = {
  colors: {
    primary: "#FF6B35",
    secondary: "#2E86AB",
    accent: "#A23B72",
    background: "#F18F01",
    text: "#FFFFFF"
  },
  timing: {
    introLength: 2,
    outroLength: 3,
    subtitleDelay: 0.5
  },
  effects: {
    enableParticles: true,
    enableGlow: true,
    enableShimmer: true,
    animationSpeed: 1
  },
  audio: {
    volume: 0.8,
    fadeIn: 1,
    fadeOut: 2
  }
};

export const VideoEditorInterface: React.FC<VideoEditorInterfaceProps> = ({
  onSettingsChange
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const [settings, setSettings] = useState<VideoSettings>(defaultSettings);
  const [activeTab, setActiveTab] = useState<'colors' | 'timing' | 'effects' | 'audio'>('colors');
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  
  // Animações da interface
  const panelSlide = spring({
    frame: frame,
    fps,
    config: {
      damping: 200,
      stiffness: 100,
    },
  });
  
  const glowEffect = interpolate(
    frame % (fps * 2),
    [0, fps, fps * 2],
    [0.3, 1, 0.3],
    { 
      extrapolateLeft: "clamp", 
      extrapolateRight: "clamp",
      easing: Easing.inOut(Easing.ease)
    }
  );
  
  // Atualizar configurações
  const updateSettings = (newSettings: Partial<VideoSettings>) => {
    const updatedSettings = { ...settings, ...newSettings };
    setSettings(updatedSettings);
    onSettingsChange?.(updatedSettings);
  };
  
  // Renderizar painel de cores
  const renderColorPanel = () => (
    <div style={{ padding: "20px", height: "100%" }}>
      <h3 style={{ 
        color: settings.colors.text, 
        fontSize: "20px", 
        marginBottom: "20px",
        textShadow: "1px 1px 2px rgba(0,0,0,0.7)"
      }}>
        🎨 Esquema de Cores
      </h3>
      
      {Object.entries(settings.colors).map(([key, value]) => (
        <div key={key} style={{ marginBottom: "15px" }}>
          <label style={{ 
            color: settings.colors.text, 
            fontSize: "14px", 
            display: "block",
            marginBottom: "5px",
            textTransform: "capitalize"
          }}>
            {key}
          </label>
          <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
            <input
              type="color"
              value={value}
              onChange={(e) => updateSettings({
                colors: { ...settings.colors, [key]: e.target.value }
              })}
              style={{
                width: "40px",
                height: "40px",
                border: "none",
                borderRadius: "8px",
                cursor: "pointer",
                boxShadow: "0 2px 8px rgba(0,0,0,0.3)"
              }}
            />
            <input
              type="text"
              value={value}
              onChange={(e) => updateSettings({
                colors: { ...settings.colors, [key]: e.target.value }
              })}
              style={{
                flex: 1,
                padding: "8px 12px",
                background: "rgba(255,255,255,0.1)",
                border: `1px solid ${settings.colors.accent}40`,
                borderRadius: "6px",
                color: settings.colors.text,
                fontSize: "12px"
              }}
            />
          </div>
        </div>
      ))}
    </div>
  );
  
  // Renderizar painel de timing
  const renderTimingPanel = () => (
    <div style={{ padding: "20px", height: "100%" }}>
      <h3 style={{ 
        color: settings.colors.text, 
        fontSize: "20px", 
        marginBottom: "20px",
        textShadow: "1px 1px 2px rgba(0,0,0,0.7)"
      }}>
        ⏱️ Configurações de Tempo
      </h3>
      
      {Object.entries(settings.timing).map(([key, value]) => (
        <div key={key} style={{ marginBottom: "20px" }}>
          <label style={{ 
            color: settings.colors.text, 
            fontSize: "14px", 
            display: "block",
            marginBottom: "8px",
            textTransform: "capitalize"
          }}>
            {key.replace(/([A-Z])/g, ' $1').trim()}: {value}s
          </label>
          <input
            type="range"
            min="0.5"
            max="10"
            step="0.5"
            value={value}
            onChange={(e) => updateSettings({
              timing: { ...settings.timing, [key]: parseFloat(e.target.value) }
            })}
            style={{
              width: "100%",
              height: "6px",
              background: `linear-gradient(90deg, ${settings.colors.primary}, ${settings.colors.accent})`,
              borderRadius: "3px",
              outline: "none",
              cursor: "pointer"
            }}
          />
        </div>
      ))}
    </div>
  );
  
  // Renderizar painel de efeitos
  const renderEffectsPanel = () => (
    <div style={{ padding: "20px", height: "100%" }}>
      <h3 style={{ 
        color: settings.colors.text, 
        fontSize: "20px", 
        marginBottom: "20px",
        textShadow: "1px 1px 2px rgba(0,0,0,0.7)"
      }}>
        ✨ Efeitos Visuais
      </h3>
      
      {Object.entries(settings.effects).map(([key, value]) => (
        <div key={key} style={{ marginBottom: "20px" }}>
          <label style={{ 
            color: settings.colors.text, 
            fontSize: "14px", 
            display: "flex",
            alignItems: "center",
            gap: "10px",
            cursor: "pointer"
          }}>
            {typeof value === 'boolean' ? (
              <input
                type="checkbox"
                checked={value}
                onChange={(e) => updateSettings({
                  effects: { ...settings.effects, [key]: e.target.checked }
                })}
                style={{
                  width: "18px",
                  height: "18px",
                  accentColor: settings.colors.accent
                }}
              />
            ) : (
              <input
                type="range"
                min="0.1"
                max="3"
                step="0.1"
                value={value}
                onChange={(e) => updateSettings({
                  effects: { ...settings.effects, [key]: parseFloat(e.target.value) }
                })}
                style={{
                  width: "100%",
                  height: "6px",
                  background: `linear-gradient(90deg, ${settings.colors.primary}, ${settings.colors.accent})`,
                  borderRadius: "3px",
                  outline: "none",
                  cursor: "pointer"
                }}
              />
            )}
            <span style={{ textTransform: "capitalize" }}>
              {key.replace(/([A-Z])/g, ' $1').trim()}
              {typeof value === 'number' && `: ${value}`}
            </span>
          </label>
        </div>
      ))}
    </div>
  );
  
  // Renderizar painel de áudio
  const renderAudioPanel = () => (
    <div style={{ padding: "20px", height: "100%" }}>
      <h3 style={{ 
        color: settings.colors.text, 
        fontSize: "20px", 
        marginBottom: "20px",
        textShadow: "1px 1px 2px rgba(0,0,0,0.7)"
      }}>
        🔊 Configurações de Áudio
      </h3>
      
      {Object.entries(settings.audio).map(([key, value]) => (
        <div key={key} style={{ marginBottom: "20px" }}>
          <label style={{ 
            color: settings.colors.text, 
            fontSize: "14px", 
            display: "block",
            marginBottom: "8px",
            textTransform: "capitalize"
          }}>
            {key.replace(/([A-Z])/g, ' $1').trim()}: {key === 'volume' ? Math.round(value * 100) + '%' : value + 's'}
          </label>
          <input
            type="range"
            min={key === 'volume' ? 0 : 0.1}
            max={key === 'volume' ? 1 : 5}
            step={key === 'volume' ? 0.01 : 0.1}
            value={value}
            onChange={(e) => updateSettings({
              audio: { ...settings.audio, [key]: parseFloat(e.target.value) }
            })}
            style={{
              width: "100%",
              height: "6px",
              background: `linear-gradient(90deg, ${settings.colors.primary}, ${settings.colors.accent})`,
              borderRadius: "3px",
              outline: "none",
              cursor: "pointer"
            }}
          />
        </div>
      ))}
    </div>
  );
  
  return (
    <AbsoluteFill>
      {/* Interface principal */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `linear-gradient(135deg, ${settings.colors.primary}20, ${settings.colors.secondary}20)`,
          backdropFilter: "blur(20px)",
          display: "flex",
          transform: `translateX(${panelSlide * 100 - 100}%)`,
        }}
      >
        {/* Painel lateral */}
        <div
          style={{
            width: "400px",
            background: `linear-gradient(180deg, ${settings.colors.primary}F0, ${settings.colors.secondary}F0)`,
            borderRight: `3px solid ${settings.colors.accent}`,
            boxShadow: `0 0 ${20 * glowEffect}px ${settings.colors.primary}40`,
            display: "flex",
            flexDirection: "column",
          }}
        >
          {/* Header */}
          <div
            style={{
              padding: "20px",
              background: `linear-gradient(135deg, ${settings.colors.accent}, ${settings.colors.primary})`,
              borderBottom: `2px solid ${settings.colors.text}20`,
            }}
          >
            <h2 style={{ 
              color: settings.colors.text, 
              fontSize: "24px", 
              margin: 0,
              textShadow: "2px 2px 4px rgba(0,0,0,0.7)",
              display: "flex",
              alignItems: "center",
              gap: "10px"
            }}>
              🎬 Editor Profissional
            </h2>
          </div>
          
          {/* Tabs */}
          <div
            style={{
              display: "flex",
              background: `${settings.colors.background}40`,
              borderBottom: `2px solid ${settings.colors.text}20`,
            }}
          >
            {(['colors', 'timing', 'effects', 'audio'] as const).map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                style={{
                  flex: 1,
                  padding: "12px 8px",
                  background: activeTab === tab 
                    ? `linear-gradient(135deg, ${settings.colors.accent}, ${settings.colors.primary})`
                    : "transparent",
                  color: settings.colors.text,
                  border: "none",
                  fontSize: "12px",
                  fontWeight: "600",
                  cursor: "pointer",
                  textTransform: "capitalize",
                  textShadow: "1px 1px 2px rgba(0,0,0,0.7)",
                  transition: "all 0.3s ease",
                  borderBottom: activeTab === tab ? `3px solid ${settings.colors.text}` : "none",
                }}
              >
                {tab}
              </button>
            ))}
          </div>
          
          {/* Conteúdo do painel */}
          <div style={{ flex: 1, overflow: "auto" }}>
            {activeTab === 'colors' && renderColorPanel()}
            {activeTab === 'timing' && renderTimingPanel()}
            {activeTab === 'effects' && renderEffectsPanel()}
            {activeTab === 'audio' && renderAudioPanel()}
          </div>
          
          {/* Footer com controles */}
          <div
            style={{
              padding: "20px",
              background: `linear-gradient(135deg, ${settings.colors.secondary}E6, ${settings.colors.primary}E6)`,
              borderTop: `2px solid ${settings.colors.text}20`,
              display: "flex",
              gap: "10px",
            }}
          >
            <button
              onClick={() => setIsPreviewMode(!isPreviewMode)}
              style={{
                flex: 1,
                padding: "12px 16px",
                background: `linear-gradient(135deg, ${settings.colors.accent}, ${settings.colors.primary})`,
                color: settings.colors.text,
                border: "none",
                borderRadius: "8px",
                fontSize: "14px",
                fontWeight: "600",
                cursor: "pointer",
                textShadow: "1px 1px 2px rgba(0,0,0,0.7)",
                boxShadow: "0 4px 15px rgba(0,0,0,0.3)",
              }}
            >
              {isPreviewMode ? "📝 Editar" : "👁️ Preview"}
            </button>
            
            <button
              onClick={() => setSettings(defaultSettings)}
              style={{
                flex: 1,
                padding: "12px 16px",
                background: `linear-gradient(135deg, ${settings.colors.secondary}, ${settings.colors.accent})`,
                color: settings.colors.text,
                border: "none",
                borderRadius: "8px",
                fontSize: "14px",
                fontWeight: "600",
                cursor: "pointer",
                textShadow: "1px 1px 2px rgba(0,0,0,0.7)",
                boxShadow: "0 4px 15px rgba(0,0,0,0.3)",
              }}
            >
              🔄 Reset
            </button>
          </div>
        </div>
        
        {/* Área de preview */}
        <div
          style={{
            flex: 1,
            padding: "20px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <div
            style={{
              width: "600px",
              height: "400px",
              background: `linear-gradient(135deg, ${settings.colors.primary}40, ${settings.colors.secondary}40)`,
              borderRadius: "20px",
              border: `3px solid ${settings.colors.accent}`,
              boxShadow: `0 0 ${30 * glowEffect}px ${settings.colors.primary}40, 0 20px 40px rgba(0,0,0,0.3)`,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              position: "relative",
              overflow: "hidden",
            }}
          >
            {/* Preview placeholder */}
            <div
              style={{
                color: settings.colors.text,
                fontSize: "24px",
                fontWeight: "600",
                textShadow: "2px 2px 4px rgba(0,0,0,0.7)",
                textAlign: "center",
              }}
            >
              {isPreviewMode ? "🎥 Preview Mode" : "📱 Instagram Preview"}
              <div style={{ fontSize: "16px", marginTop: "10px", opacity: 0.7 }}>
                {isPreviewMode ? "Visualização em tempo real" : "Configurações aplicadas"}
              </div>
            </div>
            
            {/* Elementos decorativos */}
            <div
              style={{
                position: "absolute",
                top: "10%",
                left: "10%",
                fontSize: "40px",
                opacity: 0.3,
                transform: `rotate(${frame * 0.5}deg)`,
              }}
            >
              ⚙️
            </div>
            
            <div
              style={{
                position: "absolute",
                bottom: "10%",
                right: "10%",
                fontSize: "40px",
                opacity: 0.3,
                transform: `rotate(${-frame * 0.5}deg)`,
              }}
            >
              🎨
            </div>
          </div>
        </div>
      </div>
    </AbsoluteFill>
  );
};
