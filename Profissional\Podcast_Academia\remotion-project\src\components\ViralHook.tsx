import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { Animated, Move, Fade, Scale, Rotate } from 'remotion-animated';

interface ViralHookProps {
  colors: {
    primary: string;
    secondary: string;
    accent: string;
  };
  duration?: number;
}

export const ViralHook: React.FC<ViralHookProps> = ({
  colors,
  duration = 90, // 3 segundos a 30fps
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // Hook só aparece nos primeiros segundos
  if (frame > duration) return null;

  // Animações de impacto para capturar atenção
  const hookOpacity = interpolate(
    frame,
    [0, 15, duration - 15, duration],
    [0, 1, 1, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  const explosionScale = interpolate(
    frame,
    [0, 10, 20],
    [0, 1.5, 1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  // Efeito de pulsação para urgência
  const pulseScale = interpolate(
    frame,
    [0, 15, 30],
    [1, 1.1, 1],
    { extrapolateRight: 'mirror' }
  );

  // Contador regressivo para urgência
  const countdown = Math.max(0, Math.ceil((duration - frame) / fps));

  return (
    <div
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 50,
        opacity: hookOpacity,
      }}
    >
      {/* Background com gradiente dinâmico */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `radial-gradient(circle at center, ${colors.primary}20, transparent 70%)`,
          backdropFilter: 'blur(2px)',
        }}
      />

      {/* Explosão de entrada */}
      <Animated
        animations={[
          Scale({ by: explosionScale, initial: 0 }),
          Fade({ to: 1, initial: 0 }),
          Rotate({ degrees: 360, start: 0 }),
        ]}
      >
        <div
          style={{
            position: 'absolute',
            top: '15%',
            left: '50%',
            transform: 'translateX(-50%)',
            textAlign: 'center',
          }}
        >
          {/* Emoji de impacto */}
          <div
            style={{
              fontSize: '4rem',
              marginBottom: '20px',
              transform: `scale(${pulseScale})`,
            }}
          >
            🚀💡⚡
          </div>

          {/* Texto de hook principal */}
          <div
            style={{
              fontSize: '2.5rem',
              fontWeight: 'bold',
              color: 'white',
              textShadow: '0 4px 20px rgba(0,0,0,0.8)',
              marginBottom: '15px',
              background: `linear-gradient(45deg, ${colors.accent}, ${colors.primary})`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
            }}
          >
            ATENÇÃO!
          </div>

          {/* Subtexto urgente */}
          <div
            style={{
              fontSize: '1.5rem',
              color: colors.secondary,
              fontWeight: '600',
              textShadow: '0 2px 10px rgba(0,0,0,0.8)',
              marginBottom: '20px',
            }}
          >
            Você está prestes a descobrir...
          </div>

          {/* Contador de urgência */}
          <Animated
            animations={[
              Scale({ by: pulseScale, initial: 0.9 }),
              Fade({ to: 1, initial: 0, start: 30 }),
            ]}
          >
            <div
              style={{
                display: 'inline-block',
                padding: '15px 30px',
                background: `linear-gradient(45deg, ${colors.accent}, #ff4444)`,
                borderRadius: '50px',
                border: '3px solid white',
                boxShadow: '0 10px 30px rgba(0,0,0,0.5)',
                transform: `scale(${pulseScale})`,
              }}
            >
              <div
                style={{
                  fontSize: '1.2rem',
                  fontWeight: 'bold',
                  color: 'white',
                  textShadow: '0 2px 10px rgba(0,0,0,0.5)',
                }}
              >
                ⏰ {countdown}s
              </div>
            </div>
          </Animated>
        </div>
      </Animated>

      {/* Partículas de energia */}
      {[...Array(8)].map((_, i) => (
        <Animated
          key={i}
          animations={[
            Move({
              x: Math.cos((i * Math.PI * 2) / 8) * 200,
              y: Math.sin((i * Math.PI * 2) / 8) * 200,
              start: i * 3,
            }),
            Fade({ to: 0, initial: 1, start: i * 3 + 30 }),
            Scale({ by: 0, initial: 1, start: i * 3 + 30 }),
          ]}
        >
          <div
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              width: '20px',
              height: '20px',
              background: colors.accent,
              borderRadius: '50%',
              boxShadow: `0 0 20px ${colors.accent}`,
              transform: 'translate(-50%, -50%)',
            }}
          />
        </Animated>
      ))}

      {/* Elementos de destaque nas bordas */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '8px',
          background: `linear-gradient(90deg, ${colors.accent}, ${colors.primary}, ${colors.accent})`,
          boxShadow: `0 0 20px ${colors.accent}`,
          opacity: pulseScale - 0.5,
        }}
      />
      
      <div
        style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          height: '8px',
          background: `linear-gradient(90deg, ${colors.accent}, ${colors.primary}, ${colors.accent})`,
          boxShadow: `0 0 20px ${colors.accent}`,
          opacity: pulseScale - 0.5,
        }}
      />
    </div>
  );
};
