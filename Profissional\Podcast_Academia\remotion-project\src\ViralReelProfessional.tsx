import React, { useEffect, useRef, useState } from "react";
import {
  AbsoluteFill,
  Video,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  spring,
  Sequence,
  staticFile,
  Audio,
  Artifact,
  random,
  Easing,
} from "remotion";
import * as THREE from "three";

interface ViralReelProfessionalProps {
  videoPath: string;
  audioPath?: string;
  startTime: number;
  duration: number;
}

export const ViralReelProfessional: React.FC<ViralReelProfessionalProps> = ({
  videoPath,
  audioPath,
  startTime,
  duration,
}) => {
  const frame = useCurrentFrame();
  const { fps, durationInFrames } = useVideoConfig();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const particlesRef = useRef<THREE.Points | null>(null);
  const [isThreeJSReady, setIsThreeJSReady] = useState(false);

  // Timing calculations
  const currentTime = frame / fps;
  const progress = currentTime / duration;
  
  // VIRAL MOMENTS TIMESTAMPS (baseados no vídeo real)
  const viralMoments = [
    { time: 0, text: "🚀 MÉTODO REVELADO", color: "#FF6B35", intensity: 1.0 },
    { time: 12, text: "💰 R$ 25 MIL", color: "#FF1744", intensity: 1.2 },
    { time: 24, text: "⚡ PRIMEIRO CLIENTE", color: "#00E676", intensity: 1.1 },
    { time: 36, text: "🎯 ESTRATÉGIA SECRETA", color: "#2196F3", intensity: 1.3 },
    { time: 48, text: "🔥 TRANSFORMAÇÃO", color: "#FF9800", intensity: 1.0 },
  ];

  // Find current viral moment
  const currentMoment = viralMoments.reduce((closest, moment) => {
    return Math.abs(currentTime - moment.time) < Math.abs(currentTime - closest.time) ? moment : closest;
  }, viralMoments[0]);

  // Initialize Three.js scene
  useEffect(() => {
    if (!canvasRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, 1080 / 1920, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ 
      canvas: canvasRef.current,
      alpha: true,
      antialias: true,
      premultipliedAlpha: false,
    });
    
    renderer.setSize(1080, 1920);
    renderer.setClearColor(0x000000, 0);
    renderer.setPixelRatio(window.devicePixelRatio);
    
    // Enhanced particle system with multiple layers
    const particleCount = 15000;
    const particles = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);
    const sizes = new Float32Array(particleCount);
    const velocities = new Float32Array(particleCount * 3);
    const lifetimes = new Float32Array(particleCount);
    
    // Initialize particles
    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      
      // Positions in spherical distribution
      const radius = Math.random() * 800 + 200;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.acos(2 * Math.random() - 1);
      
      positions[i3] = radius * Math.sin(phi) * Math.cos(theta);
      positions[i3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
      positions[i3 + 2] = radius * Math.cos(phi);
      
      // Colors with viral theme
      const colorIndex = Math.floor(Math.random() * 5);
      const viralColors = [
        new THREE.Color(0xFF6B35), // Orange
        new THREE.Color(0xFF1744), // Red
        new THREE.Color(0x00E676), // Green
        new THREE.Color(0x2196F3), // Blue
        new THREE.Color(0xFF9800), // Amber
      ];
      
      const color = viralColors[colorIndex];
      colors[i3] = color.r;
      colors[i3 + 1] = color.g;
      colors[i3 + 2] = color.b;
      
      // Sizes and velocities
      sizes[i] = Math.random() * 8 + 2;
      velocities[i3] = (Math.random() - 0.5) * 2;
      velocities[i3 + 1] = (Math.random() - 0.5) * 2;
      velocities[i3 + 2] = (Math.random() - 0.5) * 2;
      lifetimes[i] = Math.random();
    }
    
    particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    particles.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
    particles.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
    particles.setAttribute('lifetime', new THREE.BufferAttribute(lifetimes, 1));
    
    // Advanced particle material with custom shader
    const particleMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        pointTexture: { value: new THREE.TextureLoader().load('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTYiIGZpbGw9InVybCgjZ3JhZGllbnQwX3JhZGlhbF8xXzEpIiBmaWxsLW9wYWNpdHk9IjAuOCIvPgo8ZGVmcz4KPHJhZGlhbEdyYWRpZW50IGlkPSJncmFkaWVudDBfcmFkaWFsXzFfMSIgY3g9IjAiIGN5PSIwIiByPSIxIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgZ3JhZGllbnRUcmFuc2Zvcm09InRyYW5zbGF0ZSgxNiAxNikgcm90YXRlKDkwKSBzY2FsZSgxNikiPgo8c3RvcCBzdG9wLWNvbG9yPSIjRkZGRkZGIiBzdG9wLW9wYWNpdHk9IjEiLz4KPHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSIjRkZGRkZGIiBzdG9wLW9wYWNpdHk9IjAiLz4KPC9yYWRpYWxHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K') },
        intensity: { value: 1.0 },
        viralColor: { value: new THREE.Color(0xFF6B35) },
      },
      vertexShader: `
        attribute float size;
        attribute vec3 velocity;
        attribute float lifetime;
        
        uniform float time;
        uniform float intensity;
        
        varying vec3 vColor;
        varying float vLifetime;
        
        void main() {
          vColor = color;
          vLifetime = lifetime;
          
          vec3 pos = position;
          
          // Animated movement
          pos += velocity * time * 0.1;
          
          // Orbital motion
          float angle = time * 0.5 + lifetime * 6.28318;
          pos.x += sin(angle) * 50.0 * intensity;
          pos.y += cos(angle) * 50.0 * intensity;
          
          // Pulsation effect
          float pulse = sin(time * 3.0 + lifetime * 10.0) * 0.5 + 0.5;
          float finalSize = size * (1.0 + pulse * intensity);
          
          vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);
          
          gl_PointSize = finalSize * (300.0 / -mvPosition.z);
          gl_Position = projectionMatrix * mvPosition;
        }
      `,
      fragmentShader: `
        uniform sampler2D pointTexture;
        uniform vec3 viralColor;
        
        varying vec3 vColor;
        varying float vLifetime;
        
        void main() {
          vec4 texColor = texture2D(pointTexture, gl_PointCoord);
          
          // Mix with viral color
          vec3 finalColor = mix(vColor, viralColor, 0.3);
          
          // Alpha based on lifetime and texture
          float alpha = texColor.a * (sin(vLifetime * 3.14159) * 0.5 + 0.5);
          
          gl_FragColor = vec4(finalColor, alpha);
        }
      `,
      blending: THREE.AdditiveBlending,
      depthTest: false,
      transparent: true,
      vertexColors: true,
    });
    
    const particleSystem = new THREE.Points(particles, particleMaterial);
    scene.add(particleSystem);
    
    // Store references
    sceneRef.current = scene;
    rendererRef.current = renderer;
    particlesRef.current = particleSystem;
    
    camera.position.z = 500;
    
    setIsThreeJSReady(true);
    
    return () => {
      renderer.dispose();
      particles.dispose();
      particleMaterial.dispose();
    };
  }, []);

  // Update Three.js animation
  useEffect(() => {
    if (!isThreeJSReady || !rendererRef.current || !sceneRef.current || !particlesRef.current) return;
    
    const material = particlesRef.current.material as THREE.ShaderMaterial;
    
    // Update uniforms based on current frame
    material.uniforms.time.value = currentTime;
    material.uniforms.intensity.value = currentMoment.intensity;
    material.uniforms.viralColor.value = new THREE.Color(currentMoment.color);
    
    // Animate particles based on viral moments
    const geometry = particlesRef.current.geometry;
    const positions = geometry.attributes.position.array as Float32Array;
    const velocities = geometry.attributes.velocity.array as Float32Array;
    const sizes = geometry.attributes.size.array as Float32Array;
    
    // Update particle positions and sizes
    for (let i = 0; i < positions.length; i += 3) {
      const index = i / 3;
      
      // Explosive effect during viral moments
      const explosionStrength = currentMoment.intensity * Math.sin(currentTime * 10 + index * 0.01);
      
      velocities[i] += Math.sin(currentTime * 5 + index * 0.1) * explosionStrength * 0.1;
      velocities[i + 1] += Math.cos(currentTime * 5 + index * 0.1) * explosionStrength * 0.1;
      velocities[i + 2] += Math.sin(currentTime * 3 + index * 0.05) * explosionStrength * 0.1;
      
      // Update sizes for pulsation
      sizes[index] = (Math.sin(currentTime * 8 + index * 0.02) * 0.5 + 0.5) * 10 * currentMoment.intensity;
    }
    
    geometry.attributes.position.needsUpdate = true;
    geometry.attributes.velocity.needsUpdate = true;
    geometry.attributes.size.needsUpdate = true;
    
    // Render the scene
    rendererRef.current.render(sceneRef.current, sceneRef.current.children[0] as THREE.Camera);
  }, [currentTime, currentMoment, isThreeJSReady]);

  // Advanced animations
  const titleScale = spring({
    frame: frame % (fps * 2),
    fps,
    config: { damping: 12, stiffness: 200, mass: 0.5 }
  });

  const viralTextOpacity = interpolate(
    frame % (fps * 1.5),
    [0, fps * 0.3, fps * 1.2, fps * 1.5],
    [0, 1, 1, 0],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );

  const backgroundRotation = interpolate(
    frame,
    [0, durationInFrames],
    [0, 360],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );

  const pulseScale = interpolate(
    frame % (fps * 0.5),
    [0, fps * 0.25, fps * 0.5],
    [1, 1.1, 1],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );

  return (
    <AbsoluteFill>
      {/* Dynamic gradient background */}
      <AbsoluteFill
        style={{
          background: `
            radial-gradient(circle at 30% 20%, ${currentMoment.color}40, transparent 50%),
            radial-gradient(circle at 70% 80%, #FF6B3540, transparent 50%),
            linear-gradient(45deg, #1a1a1a, #2d2d2d)
          `,
          transform: `rotate(${backgroundRotation * 0.1}deg)`,
        }}
      />

      {/* Three.js Particle System */}
      <AbsoluteFill>
        <canvas
          ref={canvasRef}
          style={{
            width: "100%",
            height: "100%",
            position: "absolute",
            top: 0,
            left: 0,
            zIndex: 1,
          }}
        />
      </AbsoluteFill>

      {/* Main video with advanced mask */}
      <AbsoluteFill style={{ zIndex: 2 }}>
        <div
          style={{
            position: "absolute",
            top: 200,
            left: 60,
            right: 60,
            bottom: 300,
            borderRadius: 30,
            overflow: "hidden",
            boxShadow: `
              0 0 0 4px ${currentMoment.color},
              0 0 0 8px rgba(255, 255, 255, 0.1),
              0 20px 60px rgba(0, 0, 0, 0.5),
              inset 0 0 0 2px rgba(255, 255, 255, 0.1)
            `,
            transform: `scale(${pulseScale})`,
            background: `linear-gradient(135deg, ${currentMoment.color}20, transparent)`,
          }}
        >
          <Video
            src={staticFile(videoPath)}
            startFrom={Math.floor(startTime * fps)}
            style={{
              width: "100%",
              height: "100%",
              objectFit: "cover",
              filter: `
                brightness(1.1) 
                contrast(1.2) 
                saturate(1.3) 
                hue-rotate(${frame * 0.5}deg)
              `,
            }}
            volume={0.7}
          />
          
          {/* Video overlay effects */}
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: `
                radial-gradient(circle at center, transparent 30%, ${currentMoment.color}10 70%),
                linear-gradient(90deg, transparent 0%, ${currentMoment.color}05 50%, transparent 100%)
              `,
              pointerEvents: "none",
            }}
          />
        </div>
      </AbsoluteFill>

      {/* Viral text overlay */}
      <AbsoluteFill style={{ zIndex: 3 }}>
        <div
          style={{
            position: "absolute",
            top: 60,
            left: 40,
            right: 40,
            textAlign: "center",
            opacity: viralTextOpacity,
            transform: `scale(${titleScale})`,
          }}
        >
          <div
            style={{
              fontSize: 72,
              fontWeight: "900",
              color: currentMoment.color,
              textShadow: `
                0 0 20px ${currentMoment.color},
                0 0 40px ${currentMoment.color}80,
                4px 4px 0px #000,
                -4px -4px 0px #000,
                4px -4px 0px #000,
                -4px 4px 0px #000
              `,
              lineHeight: 0.9,
              letterSpacing: "-0.02em",
              background: `linear-gradient(45deg, ${currentMoment.color}, #FFF)`,
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              animation: "pulse 0.5s ease-in-out infinite alternate",
            }}
          >
            {currentMoment.text}
          </div>
        </div>
      </AbsoluteFill>

      {/* Progress bar with viral styling */}
      <AbsoluteFill style={{ zIndex: 4 }}>
        <div
          style={{
            position: "absolute",
            bottom: 40,
            left: 40,
            right: 40,
            height: 8,
            background: "rgba(255, 255, 255, 0.1)",
            borderRadius: 4,
            overflow: "hidden",
            backdropFilter: "blur(10px)",
            boxShadow: `0 0 0 1px rgba(255, 255, 255, 0.1)`,
          }}
        >
          <div
            style={{
              height: "100%",
              background: `linear-gradient(90deg, ${currentMoment.color}, #FF6B35)`,
              borderRadius: 4,
              width: `${progress * 100}%`,
              boxShadow: `
                0 0 20px ${currentMoment.color},
                0 0 40px ${currentMoment.color}80
              `,
              transition: "all 0.3s ease",
            }}
          />
        </div>
      </AbsoluteFill>

      {/* Floating engagement elements */}
      <AbsoluteFill style={{ zIndex: 5 }}>
        {[...Array(8)].map((_, i) => {
          const elementProgress = interpolate(
            (frame + i * 15) % (fps * 3),
            [0, fps * 3],
            [0, 1]
          );
          
          const yPosition = interpolate(
            elementProgress,
            [0, 1],
            [1920, -100]
          );
          
          const opacity = interpolate(
            elementProgress,
            [0, 0.2, 0.8, 1],
            [0, 1, 1, 0]
          );
          
          const emojis = ["🔥", "⚡", "💰", "🚀", "🎯", "💎", "⭐", "🌟"];
          
          return (
            <div
              key={i}
              style={{
                position: "absolute",
                left: `${20 + (i * 10)}%`,
                top: yPosition,
                fontSize: 40,
                opacity,
                transform: `
                  rotate(${frame * 2 + i * 45}deg) 
                  scale(${1 + Math.sin(frame * 0.1 + i) * 0.3})
                `,
                filter: `drop-shadow(0 0 10px ${currentMoment.color})`,
              }}
            >
              {emojis[i]}
            </div>
          );
        })}
      </AbsoluteFill>

      {/* Audio */}
      {audioPath && (
        <Audio
          src={staticFile(audioPath)}
          startFrom={Math.floor(startTime * fps)}
          volume={0.8}
        />
      )}

      {/* Export metadata */}
      {frame === 0 && (
        <Artifact
          filename="viral-reel-metadata.json"
          content={JSON.stringify({
            type: "viral-instagram-reel",
            duration: duration,
            viralMoments: viralMoments,
            features: [
              "3D particle system",
              "Dynamic color adaptation",
              "Advanced visual effects",
              "Viral moment detection",
              "Professional animations"
            ],
            tech: ["Three.js", "Remotion", "WebGL", "Custom Shaders"],
            optimizedFor: "maximum viral potential"
          }, null, 2)}
        />
      )}
    </AbsoluteFill>
  );
};
