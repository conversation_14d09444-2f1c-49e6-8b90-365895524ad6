[0715/084917.951:ERROR:gl_factory.cc(103)] Requested GL implementation (gl=egl-gles2,angle=none) not found in allowed implementations: [(gl=egl-angle,angle=default)].
[0715/084917.951:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/084917.953:ERROR:viz_main_impl.cc(183)] Exiting GPU process due to errors during initialization
[0715/085041.830:ERROR:gl_factory.cc(103)] Requested GL implementation (gl=egl-gles2,angle=none) not found in allowed implementations: [(gl=egl-angle,angle=default)].
[0715/085042.045:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/085042.103:ERROR:viz_main_impl.cc(183)] Exiting GPU process due to errors during initialization
[0715/085450.840:ERROR:gl_factory.cc(103)] Requested GL implementation (gl=egl-gles2,angle=none) not found in allowed implementations: [(gl=egl-angle,angle=default)].
[0715/085450.840:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/085450.840:ERROR:viz_main_impl.cc(183)] Exiting GPU process due to errors during initialization
[0715/085617.885:ERROR:gl_factory.cc(103)] Requested GL implementation (gl=egl-gles2,angle=none) not found in allowed implementations: [(gl=egl-angle,angle=default)].
[0715/085617.885:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/085617.885:ERROR:viz_main_impl.cc(183)] Exiting GPU process due to errors during initialization
[0715/085639.608:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/085640.270:ERROR:command_buffer_proxy_impl.cc(125)] ContextResult::kTransientFailure: Failed to send GpuControl.CreateCommandBuffer.
[0715/085640.268:ERROR:command_buffer_proxy_impl.cc(125)] ContextResult::kTransientFailure: Failed to send GpuControl.CreateCommandBuffer.
[0715/090119.759:ERROR:gl_factory.cc(103)] Requested GL implementation (gl=egl-gles2,angle=none) not found in allowed implementations: [(gl=egl-angle,angle=default)].
[0715/090120.005:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/090120.005:ERROR:viz_main_impl.cc(183)] Exiting GPU process due to errors during initialization
[0715/090126.779:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/090126.790:ERROR:command_buffer_proxy_impl.cc(125)] ContextResult::kTransientFailure: Failed to send GpuControl.CreateCommandBuffer.
[0715/090700.409:ERROR:gl_factory.cc(103)] Requested GL implementation (gl=egl-gles2,angle=none) not found in allowed implementations: [(gl=egl-angle,angle=default)].
[0715/090700.409:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/090700.409:ERROR:viz_main_impl.cc(183)] Exiting GPU process due to errors during initialization
[0715/090706.451:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/090706.543:ERROR:command_buffer_proxy_impl.cc(125)] ContextResult::kTransientFailure: Failed to send GpuControl.CreateCommandBuffer.
[0715/105729.452:ERROR:gl_factory.cc(103)] Requested GL implementation (gl=egl-gles2,angle=none) not found in allowed implementations: [(gl=egl-angle,angle=default)].
[0715/105729.454:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/105729.455:ERROR:viz_main_impl.cc(183)] Exiting GPU process due to errors during initialization
[0715/105734.044:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/105734.054:ERROR:command_buffer_proxy_impl.cc(125)] ContextResult::kTransientFailure: Failed to send GpuControl.CreateCommandBuffer.
[0715/105807.332:ERROR:gl_factory.cc(103)] Requested GL implementation (gl=egl-gles2,angle=none) not found in allowed implementations: [(gl=egl-angle,angle=default)].
[0715/105807.334:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/105807.334:ERROR:viz_main_impl.cc(183)] Exiting GPU process due to errors during initialization
[0715/105811.712:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/105811.725:ERROR:command_buffer_proxy_impl.cc(125)] ContextResult::kTransientFailure: Failed to send GpuControl.CreateCommandBuffer.
[0715/105915.578:ERROR:gl_factory.cc(103)] Requested GL implementation (gl=egl-gles2,angle=none) not found in allowed implementations: [(gl=egl-angle,angle=default)].
[0715/105915.579:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/105915.579:ERROR:viz_main_impl.cc(183)] Exiting GPU process due to errors during initialization
[0715/105920.239:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/105920.245:ERROR:command_buffer_proxy_impl.cc(125)] ContextResult::kTransientFailure: Failed to send GpuControl.CreateCommandBuffer.
[0715/110000.834:ERROR:gl_factory.cc(103)] Requested GL implementation (gl=egl-gles2,angle=none) not found in allowed implementations: [(gl=egl-angle,angle=default)].
[0715/110000.834:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/110000.834:ERROR:viz_main_impl.cc(183)] Exiting GPU process due to errors during initialization
[0715/110005.138:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/110005.143:ERROR:command_buffer_proxy_impl.cc(125)] ContextResult::kTransientFailure: Failed to send GpuControl.CreateCommandBuffer.
[0715/111317.477:ERROR:gl_factory.cc(103)] Requested GL implementation (gl=egl-gles2,angle=none) not found in allowed implementations: [(gl=egl-angle,angle=default)].
[0715/111317.477:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/111317.477:ERROR:viz_main_impl.cc(183)] Exiting GPU process due to errors during initialization
[0715/111321.885:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/111321.893:ERROR:command_buffer_proxy_impl.cc(125)] ContextResult::kTransientFailure: Failed to send GpuControl.CreateCommandBuffer.
[0715/111702.199:ERROR:gl_factory.cc(103)] Requested GL implementation (gl=egl-gles2,angle=none) not found in allowed implementations: [(gl=egl-angle,angle=default)].
[0715/111702.206:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/111702.207:ERROR:viz_main_impl.cc(183)] Exiting GPU process due to errors during initialization
[0715/111707.341:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/111707.351:ERROR:command_buffer_proxy_impl.cc(125)] ContextResult::kTransientFailure: Failed to send GpuControl.CreateCommandBuffer.
[0715/111938.848:ERROR:gl_factory.cc(103)] Requested GL implementation (gl=egl-gles2,angle=none) not found in allowed implementations: [(gl=egl-angle,angle=default)].
[0715/111938.853:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/111938.853:ERROR:viz_main_impl.cc(183)] Exiting GPU process due to errors during initialization
[0715/111943.006:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/111943.014:ERROR:command_buffer_proxy_impl.cc(125)] ContextResult::kTransientFailure: Failed to send GpuControl.CreateCommandBuffer.
[0715/114034.165:ERROR:gl_factory.cc(103)] Requested GL implementation (gl=egl-gles2,angle=none) not found in allowed implementations: [(gl=egl-angle,angle=default)].
[0715/114034.165:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/114034.165:ERROR:viz_main_impl.cc(183)] Exiting GPU process due to errors during initialization
[0715/114038.603:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/114038.611:ERROR:command_buffer_proxy_impl.cc(125)] ContextResult::kTransientFailure: Failed to send GpuControl.CreateCommandBuffer.
[0715/115524.010:ERROR:gl_factory.cc(103)] Requested GL implementation (gl=egl-gles2,angle=none) not found in allowed implementations: [(gl=egl-angle,angle=default)].
[0715/115524.010:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/115524.010:ERROR:viz_main_impl.cc(183)] Exiting GPU process due to errors during initialization
[0715/115528.340:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/115528.358:ERROR:command_buffer_proxy_impl.cc(125)] ContextResult::kTransientFailure: Failed to send GpuControl.CreateCommandBuffer.
[0715/122649.087:ERROR:gl_factory.cc(103)] Requested GL implementation (gl=egl-gles2,angle=none) not found in allowed implementations: [(gl=egl-angle,angle=default)].
[0715/122649.089:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/122649.089:ERROR:viz_main_impl.cc(183)] Exiting GPU process due to errors during initialization
[0715/122656.087:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/122656.094:ERROR:command_buffer_proxy_impl.cc(125)] ContextResult::kTransientFailure: Failed to send GpuControl.CreateCommandBuffer.
[0715/133639.166:ERROR:gl_factory.cc(103)] Requested GL implementation (gl=egl-gles2,angle=none) not found in allowed implementations: [(gl=egl-angle,angle=default)].
[0715/133639.166:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/133639.166:ERROR:viz_main_impl.cc(183)] Exiting GPU process due to errors during initialization
[0715/133652.056:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/133652.070:ERROR:command_buffer_proxy_impl.cc(125)] ContextResult::kTransientFailure: Failed to send GpuControl.CreateCommandBuffer.
[0715/140327.346:ERROR:gl_factory.cc(103)] Requested GL implementation (gl=egl-gles2,angle=none) not found in allowed implementations: [(gl=egl-angle,angle=default)].
[0715/140327.347:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/140327.347:ERROR:viz_main_impl.cc(183)] Exiting GPU process due to errors during initialization
[0715/140559.260:ERROR:gl_factory.cc(103)] Requested GL implementation (gl=egl-gles2,angle=none) not found in allowed implementations: [(gl=egl-angle,angle=default)].
[0715/140559.262:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/140559.262:ERROR:viz_main_impl.cc(183)] Exiting GPU process due to errors during initialization
[0715/140609.490:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/140617.507:ERROR:gles2_cmd_decoder_passthrough.cc(1083)] [GroupMarkerNotSet(crbug.com/242999)!:A0B05600F47B0000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader flag to opt in to lower security guarantees for trusted content.
[0715/140619.270:ERROR:gles2_cmd_decoder_passthrough.cc(1083)] [GroupMarkerNotSet(crbug.com/242999)!:A0E05600F47B0000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader flag to opt in to lower security guarantees for trusted content.
[0715/140622.334:ERROR:gles2_cmd_decoder_passthrough.cc(1083)] [GroupMarkerNotSet(crbug.com/242999)!:A0706801F47B0000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader flag to opt in to lower security guarantees for trusted content.
[0715/140624.103:ERROR:gles2_cmd_decoder_passthrough.cc(1083)] [GroupMarkerNotSet(crbug.com/242999)!:A0D06801F47B0000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader flag to opt in to lower security guarantees for trusted content.
[0715/140626.096:ERROR:gles2_cmd_decoder_passthrough.cc(1083)] [GroupMarkerNotSet(crbug.com/242999)!:A0F07A01F47B0000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader flag to opt in to lower security guarantees for trusted content.
[0715/140629.110:ERROR:gles2_cmd_decoder_passthrough.cc(1083)] [GroupMarkerNotSet(crbug.com/242999)!:A0507B01F47B0000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader flag to opt in to lower security guarantees for trusted content.
[0715/140632.484:ERROR:gles2_cmd_decoder_passthrough.cc(1083)] [GroupMarkerNotSet(crbug.com/242999)!:A030AA01F47B0000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader flag to opt in to lower security guarantees for trusted content.
[0715/140634.703:ERROR:gles2_cmd_decoder_passthrough.cc(1083)] [GroupMarkerNotSet(crbug.com/242999)!:A090AA01F47B0000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader flag to opt in to lower security guarantees for trusted content.
[0715/140858.236:ERROR:gl_factory.cc(103)] Requested GL implementation (gl=egl-gles2,angle=none) not found in allowed implementations: [(gl=egl-angle,angle=default)].
[0715/140858.244:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/140858.248:ERROR:viz_main_impl.cc(183)] Exiting GPU process due to errors during initialization
[0715/140917.240:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/140917.259:ERROR:command_buffer_proxy_impl.cc(125)] ContextResult::kTransientFailure: Failed to send GpuControl.CreateCommandBuffer.
[0715/143357.916:ERROR:gl_factory.cc(103)] Requested GL implementation (gl=egl-gles2,angle=none) not found in allowed implementations: [(gl=egl-angle,angle=default)].
[0715/143357.921:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/143357.921:ERROR:viz_main_impl.cc(183)] Exiting GPU process due to errors during initialization
[0715/143408.652:WARNING:viz_main_impl.cc(85)] VizNullHypothesis is disabled (not a warning)
[0715/143408.979:ERROR:command_buffer_proxy_impl.cc(125)] ContextResult::kTransientFailure: Failed to send GpuControl.CreateCommandBuffer.
[0715/143426.045:ERROR:gles2_cmd_decoder_passthrough.cc(1083)] [GroupMarkerNotSet(crbug.com/242999)!:A0E057002C530000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader flag to opt in to lower security guarantees for trusted content.
[0715/143427.900:ERROR:gles2_cmd_decoder_passthrough.cc(1083)] [GroupMarkerNotSet(crbug.com/242999)!:A01058002C530000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader flag to opt in to lower security guarantees for trusted content.
[0715/143431.260:ERROR:gles2_cmd_decoder_passthrough.cc(1083)] [GroupMarkerNotSet(crbug.com/242999)!:A07068012C530000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader flag to opt in to lower security guarantees for trusted content.
[0715/143435.957:ERROR:gles2_cmd_decoder_passthrough.cc(1083)] [GroupMarkerNotSet(crbug.com/242999)!:A0A068012C530000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader flag to opt in to lower security guarantees for trusted content.
[0715/143441.299:ERROR:gles2_cmd_decoder_passthrough.cc(1083)] [GroupMarkerNotSet(crbug.com/242999)!:A03079012C530000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader flag to opt in to lower security guarantees for trusted content.
[0715/143444.435:ERROR:gles2_cmd_decoder_passthrough.cc(1083)] [GroupMarkerNotSet(crbug.com/242999)!:A09079012C530000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader flag to opt in to lower security guarantees for trusted content.
[0715/143447.424:ERROR:gles2_cmd_decoder_passthrough.cc(1083)] [GroupMarkerNotSet(crbug.com/242999)!:A000AB012C530000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader flag to opt in to lower security guarantees for trusted content.
[0715/143450.105:ERROR:gles2_cmd_decoder_passthrough.cc(1083)] [GroupMarkerNotSet(crbug.com/242999)!:A090AB012C530000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader flag to opt in to lower security guarantees for trusted content.
[0715/143643.209:ERROR:gl_utils.cc(431)] [.WebGL-0x1a4400554000]GL Driver Message (OpenGL, Performance, GL_CLOSE_PATH_NV, High): GPU stall due to ReadPixels
[0715/143643.682:ERROR:gl_utils.cc(431)] [.WebGL-0x1a4400554000]GL Driver Message (OpenGL, Performance, GL_CLOSE_PATH_NV, High): GPU stall due to ReadPixels
[0715/143645.413:ERROR:gl_utils.cc(431)] [.WebGL-0x1ac000e1b00]GL Driver Message (OpenGL, Performance, GL_CLOSE_PATH_NV, High): GPU stall due to ReadPixels
[0715/143645.513:ERROR:gl_utils.cc(431)] [.WebGL-0x32fc000e6200]GL Driver Message (OpenGL, Performance, GL_CLOSE_PATH_NV, High): GPU stall due to ReadPixels (this message will no longer repeat)
