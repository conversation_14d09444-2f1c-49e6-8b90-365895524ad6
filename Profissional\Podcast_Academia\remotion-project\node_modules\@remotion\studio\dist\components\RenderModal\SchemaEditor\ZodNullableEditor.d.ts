import type { z } from 'zod';
import type { UpdaterFunction } from './ZodSwitch';
import type { JSONPath } from './zod-types';
export declare const ZodNullableEditor: React.FC<{
    showSaveButton: boolean;
    jsonPath: JSONPath;
    value: unknown;
    defaultValue: unknown;
    schema: z.ZodTypeAny;
    setValue: UpdaterFunction<unknown>;
    onSave: UpdaterFunction<unknown>;
    onRemove: null | (() => void);
    saving: boolean;
    saveDisabledByParent: boolean;
    mayPad: boolean;
}>;
