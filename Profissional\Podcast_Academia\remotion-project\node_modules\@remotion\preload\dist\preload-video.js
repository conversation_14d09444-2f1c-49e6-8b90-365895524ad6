"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.preloadVideo = void 0;
const preload_asset_1 = require("./preload-asset");
/*
 * @description Preloads a video in the DOM so that when a video tag is mounted, it can play immediately.
 * @see [Documentation](https://www.remotion.dev/docs/preload/preload-video)
 */
const preloadVideo = (src) => {
    return (0, preload_asset_1.preloadAsset)(src, 'video');
};
exports.preloadVideo = preloadVideo;
