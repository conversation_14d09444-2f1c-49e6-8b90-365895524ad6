import React from 'react';
import { useCurrentFrame, interpolate, useVideoConfig } from 'remotion';
import { Animated, Move, Fade, Scale } from 'remotion-animated';

interface ProgressBarProps {
  colors: {
    primary: string;
    secondary: string;
    accent: string;
  };
  position?: 'top' | 'bottom';
  thickness?: number;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  colors,
  position = 'bottom',
  thickness = 6,
}) => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();

  // Progresso suave com easing
  const progress = interpolate(
    frame,
    [0, durationInFrames],
    [0, 100],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  // Animação de entrada
  const barOpacity = interpolate(
    frame,
    [0, 30],
    [0, 1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  // Efeito de brilho animado
  const glowIntensity = interpolate(
    frame,
    [0, 60, 120],
    [0.3, 1, 0.3],
    { extrapolateRight: 'mirror' }
  );

  return (
    <div
      style={{
        position: 'absolute',
        [position]: 0,
        left: 0,
        right: 0,
        height: thickness * 2,
        zIndex: 20,
        opacity: barOpacity,
      }}
    >
      {/* Background da barra */}
      <div
        style={{
          width: '100%',
          height: thickness,
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
          borderRadius: thickness / 2,
        }}
      />

      {/* Barra de progresso principal */}
      <Animated
        animations={[
          Scale({ by: 1, initial: 0.8 }),
          Fade({ to: 1, initial: 0 }),
        ]}
      >
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: `${progress}%`,
            height: thickness,
            background: `linear-gradient(90deg, ${colors.primary}, ${colors.accent})`,
            borderRadius: thickness / 2,
            boxShadow: `0 0 ${thickness * 2}px ${colors.accent}${Math.round(glowIntensity * 255).toString(16).padStart(2, '0')}`,
            transition: 'width 0.1s ease-out',
          }}
        />
      </Animated>

      {/* Efeito de brilho móvel */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: `${Math.max(0, progress - 10)}%`,
          width: '20%',
          height: thickness,
          background: `linear-gradient(90deg, transparent, rgba(255, 255, 255, ${glowIntensity * 0.5}), transparent)`,
          borderRadius: thickness / 2,
          pointerEvents: 'none',
        }}
      />

      {/* Indicador de tempo */}
      <div
        style={{
          position: 'absolute',
          top: thickness + 5,
          right: 20,
          fontSize: '0.8rem',
          color: colors.secondary,
          fontWeight: '500',
          textShadow: '0 1px 3px rgba(0,0,0,0.5)',
        }}
      >
        {Math.round((progress / 100) * (durationInFrames / 30))}s / {Math.round(durationInFrames / 30)}s
      </div>
    </div>
  );
};
