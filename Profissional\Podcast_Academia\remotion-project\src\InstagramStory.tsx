import React from "react";
import {
  AbsoluteFill,
  Video,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Audio,
  Artifact,
  staticFile,
  Sequence,
  spring,
} from "remotion";
import { PodcastClip } from "./components/PodcastClip";
import { DynamicSubtitles } from "./components/DynamicSubtitles";
import { BrandingOverlay } from "./components/BrandingOverlay";

interface InstagramStoryProps {
  videoPath: string;
  startTime: number;
  duration: number;
  title: string;
  author: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
}

export const InstagramStory: React.FC<InstagramStoryProps> = ({
  videoPath,
  startTime,
  duration,
  title,
  author,
  colors
}) => {
  const frame = useCurrentFrame();
  const { fps, durationInFrames } = useVideoConfig();
  
  // Animação de entrada rápida para Stories
  const entryAnimation = spring({
    frame,
    fps,
    config: {
      damping: 20,
      stiffness: 200,
    }
  });
  
  // Efeito de zoom dinâmico
  const dynamicZoom = interpolate(
    frame,
    [0, durationInFrames],
    [1, 1.1],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );
  
  // Efeito de glow pulsante
  const glowIntensity = interpolate(
    frame % (fps * 2),
    [0, fps, fps * 2],
    [0.3, 1, 0.3],
    { extrapolateLeft: "clamp", extrapolateRight: "clamp" }
  );
  
  return (
    <AbsoluteFill>
      {/* Background gradient animado */}
      <AbsoluteFill
        style={{
          background: `radial-gradient(circle at ${50 + Math.sin(frame * 0.02) * 10}% ${50 + Math.cos(frame * 0.02) * 10}%, ${colors.primary}, ${colors.secondary})`,
          transform: `scale(${dynamicZoom})`,
        }}
      />
      
      {/* Overlay com partículas */}
      <AbsoluteFill
        style={{
          background: `radial-gradient(circle at ${Math.sin(frame * 0.03) * 50 + 50}% ${Math.cos(frame * 0.03) * 50 + 50}%, ${colors.accent}20, transparent 70%)`,
          opacity: 0.7,
        }}
      />
      
      {/* Título principal com efeito glitch */}
      <div
        style={{
          position: "absolute",
          top: 100,
          left: 40,
          right: 40,
          transform: `scale(${entryAnimation})`,
        }}
      >
        <div
          style={{
            fontSize: 48,
            fontWeight: "900",
            color: colors.text,
            textAlign: "center",
            textShadow: `0 0 ${20 * glowIntensity}px ${colors.primary}, 2px 2px 4px rgba(0,0,0,0.5)`,
            lineHeight: 1.2,
            filter: frame % 120 === 0 ? "blur(1px)" : "none", // Efeito glitch ocasional
          }}
        >
          {title}
        </div>
        
        {/* Indicador de "NOVO" */}
        <div
          style={{
            position: "absolute",
            top: -20,
            right: -20,
            background: `linear-gradient(135deg, ${colors.accent}, ${colors.primary})`,
            color: colors.text,
            padding: "4px 12px",
            borderRadius: 15,
            fontSize: 12,
            fontWeight: "bold",
            textShadow: "1px 1px 2px rgba(0,0,0,0.5)",
            transform: `rotate(15deg) scale(${entryAnimation})`,
            boxShadow: "0 4px 15px rgba(0,0,0,0.3)",
          }}
        >
          NOVO!
        </div>
      </div>
      
      {/* Vídeo principal com bordas arredondadas */}
      <div
        style={{
          position: "absolute",
          top: 200,
          left: 40,
          right: 40,
          bottom: 300,
          borderRadius: 25,
          overflow: "hidden",
          boxShadow: `0 0 ${30 * glowIntensity}px ${colors.primary}80, 0 20px 40px rgba(0,0,0,0.3)`,
          border: `3px solid ${colors.accent}`,
          transform: `scale(${entryAnimation})`,
        }}
      >
        <PodcastClip
          videoPath={videoPath}
          startTime={startTime}
          duration={duration}
          scale={1}
          colors={colors}
        />
      </div>
      
      {/* Legendas otimizadas para Stories */}
      <div
        style={{
          position: "absolute",
          bottom: 250,
          left: 30,
          right: 30,
          transform: `scale(${entryAnimation})`,
        }}
      >
        <DynamicSubtitles
          videoPath={videoPath}
          startTime={startTime}
          colors={colors}
        />
      </div>
      
      {/* Call to Action interativo */}
      <div
        style={{
          position: "absolute",
          bottom: 120,
          left: 40,
          right: 40,
          transform: `scale(${entryAnimation})`,
        }}
      >
        <div
          style={{
            background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,
            borderRadius: 25,
            padding: "16px 24px",
            textAlign: "center",
            boxShadow: `0 0 ${20 * glowIntensity}px ${colors.primary}60, 0 10px 30px rgba(0,0,0,0.3)`,
            border: `2px solid ${colors.text}`,
            animation: frame % 180 < 90 ? "pulse 2s infinite" : "none",
          }}
        >
          <div
            style={{
              color: colors.text,
              fontSize: 20,
              fontWeight: "bold",
              textShadow: "1px 1px 2px rgba(0,0,0,0.5)",
              marginBottom: 8,
            }}
          >
            👆 Deslize para cima
          </div>
          <div
            style={{
              color: colors.text,
              fontSize: 14,
              opacity: 0.9,
              textShadow: "1px 1px 2px rgba(0,0,0,0.5)",
            }}
          >
            Saiba mais sobre Agentes IA
          </div>
        </div>
      </div>
      
      {/* Progress bar para Stories */}
      <div
        style={{
          position: "absolute",
          top: 20,
          left: 20,
          right: 20,
          height: 4,
          background: `${colors.text}30`,
          borderRadius: 2,
        }}
      >
        <div
          style={{
            height: "100%",
            background: colors.text,
            borderRadius: 2,
            width: `${(frame / durationInFrames) * 100}%`,
            boxShadow: `0 0 10px ${colors.text}`,
          }}
        />
      </div>
      
      {/* Branding para Stories */}
      <BrandingOverlay
        frame={frame}
        author={author}
        colors={colors}
        position="top-left"
      />
      
      {/* Elementos flutuantes */}
      {[...Array(5)].map((_, i) => (
        <div
          key={i}
          style={{
            position: "absolute",
            left: `${20 + i * 20}%`,
            top: `${30 + Math.sin(frame * 0.01 + i) * 10}%`,
            fontSize: 30,
            opacity: 0.3,
            transform: `rotate(${frame * 0.5 + i * 45}deg)`,
            filter: "blur(1px)",
          }}
        >
          {["🤖", "⚡", "🔥", "🚀", "✨"][i]}
        </div>
      ))}
      
      {/* Emit Story-specific content */}
      {frame === 0 && (
        <Artifact
          filename="story-metadata.json"
          content={JSON.stringify({
            type: "instagram-story",
            duration: 15,
            title,
            author,
            hashtags: ["#IA", "#AgentesIA", "#Tech", "#Inovação"],
            engagement: {
              cta: "Deslize para cima",
              action: "link",
              url: "https://link.bio/agentes-ia"
            }
          }, null, 2)}
        />
      )}
    </AbsoluteFill>
  );
};
