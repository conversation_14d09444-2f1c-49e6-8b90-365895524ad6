import type { GifFillMode } from './props';
type Props = {
    readonly index: number;
    readonly frames: ImageData[];
    readonly width?: number;
    readonly height?: number;
    readonly fit: GifFillMode;
    readonly className?: string;
    readonly style?: React.CSSProperties;
};
export declare const Canvas: import("react").ForwardRefExoticComponent<Props & import("react").RefAttributes<unknown>>;
export {};
