{"repository": {"url": "https://github.com/remotion-dev/remotion/tree/main/packages/lottie"}, "name": "@remotion/lottie", "version": "4.0.324", "description": "Include Lottie animations in Remotion", "main": "dist/cjs/index.js", "types": "dist/cjs/index.d.ts", "module": "dist/esm/index.mjs", "sideEffects": false, "bugs": {"url": "https://github.com/remotion-dev/remotion/issues"}, "author": "<PERSON> <<EMAIL>>", "maintainers": ["<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "license": "SEE LICENSE IN LICENSE.md", "dependencies": {"remotion": "4.0.324"}, "peerDependencies": {"lottie-web": "^5", "react": ">=16.8.0", "react-dom": ">=16.8.0"}, "exports": {"./package.json": "./package.json", ".": {"types": "./dist/cjs/index.d.ts", "module": "./dist/esm/index.mjs", "import": "./dist/esm/index.mjs", "require": "./dist/cjs/index.js"}}, "devDependencies": {"react": "19.0.0", "react-dom": "19.0.0", "lottie-web": "5.13.0", "eslint": "9.19.0", "@remotion/eslint-config-internal": "4.0.324"}, "keywords": ["remotion", "ffmpeg", "video", "react", "lottie", "player"], "publishConfig": {"access": "public"}, "homepage": "https://www.remotion.dev/docs/lottie", "scripts": {"formatting": "prettier --experimental-cli src --check", "lint": "eslint src", "test": "bun test src", "make": "tsc -d && bun --env-file=../.env.bundle bundle.ts"}}