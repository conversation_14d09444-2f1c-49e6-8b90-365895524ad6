import React from "react";
import {
  AbsoluteFill,
  Video,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Audio,
  staticFile
} from "remotion";
import { Animated, Move, Fade, Scale, Rotate } from 'remotion-animated';
import { noise2D } from '@remotion/noise';
import { AnimatedTitle } from './AnimatedTitle';
import { ProgressBar } from './ProgressBar';
import { SocialOverlay } from './SocialOverlay';
import { ViralHook } from './ViralHook';

interface PodcastClipProps {
  videoPath: string;
  startTime: number;
  duration: number;
  scale: number;
  title?: string;
  subtitle?: string;
  author?: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
}

export const PodcastClip: React.FC<PodcastClipProps> = ({
  videoPath,
  startTime,
  duration,
  scale,
  title = "Agente IA",
  subtitle = "Construa seu primeiro Agente IA em 90 dias",
  author = "Academia",
  colors
}) => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();
  const { fps } = useVideoConfig();
  
  // Calcular o frame atual do vídeo baseado no startTime
  const videoFrame = Math.floor((startTime + (frame / fps)) * fps);
  
  return (
    <AbsoluteFill>
      {/* Container do vídeo com efeetos */}
      <div
        style={{
          width: "100%",
          height: "100%",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          transform: `scale(${scale})`,
        }}
      >
        {/* Container do vídeo com animações cinematográficas */}
        <Animated
          animations={[
            Scale({ by: 1, initial: 0.8 }),
            Fade({ to: 1, initial: 0 }),
            Move({ y: -30, start: 15 }),
          ]}
        >
          <div
            style={{
              width: "90%",
              height: "70%",
              borderRadius: 20,
              overflow: "hidden",
              boxShadow: `0 20px 40px rgba(0,0,0,0.3)`,
              border: `4px solid ${colors.accent}`,
              position: "relative",
              transform: `translateY(${Math.sin(frame * 0.02) * 2}px)`,
            }}
          >
          {/* Vídeo principal */}
          <Video
            src={staticFile(videoPath)}
            startFrom={videoFrame}
            style={{
              width: "100%",
              height: "100%",
              objectFit: "cover",
            }}
            muted={true}
            volume={0}
          />

          {/* Áudio sincronizado extraído */}
          <Audio
            src={staticFile("audio_original.mp3")}
            startFrom={videoFrame}
            volume={0.8}
          />
          
          {/* Overlay gradiente para melhor legibilidade */}
          <AbsoluteFill
            style={{
              background: `linear-gradient(transparent 60%, rgba(0,0,0,0.4) 100%)`,
              pointerEvents: "none",
            }}
          />
          
          {/* Efeito de brilho nas bordas */}
          <div
            style={{
              position: "absolute",
              top: -2,
              left: -2,
              right: -2,
              bottom: -2,
              borderRadius: 24,
              background: `linear-gradient(45deg, ${colors.primary}, ${colors.secondary}, ${colors.accent})`,
              zIndex: -1,
              opacity: 0.6,
            }}
          />
          </div>
        </Animated>

        {/* Componentes de animação profissionais */}
        <AnimatedTitle
          title={title || "Agente IA"}
          subtitle={subtitle}
          colors={colors}
          startFrame={30}
          endFrame={durationInFrames - 60}
        />

        <ProgressBar
          colors={colors}
          position="bottom"
          thickness={8}
        />

        <SocialOverlay
          author={author || "Academia"}
          colors={colors}
          showCTA={true}
        />

        {/* Hook viral para capturar atenção nos primeiros segundos */}
        <ViralHook
          colors={colors}
          duration={90}
        />
      </div>
    </AbsoluteFill>
  );
};
