import React from "react";
import {
  AbsoluteFill,
  Video,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  Audio,
  staticFile
} from "remotion";

interface PodcastClipProps {
  videoPath: string;
  startTime: number;
  duration: number;
  scale: number;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
}

export const PodcastClip: React.FC<PodcastClipProps> = ({
  videoPath,
  startTime,
  duration,
  scale,
  colors
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  // Calcular o frame atual do vídeo baseado no startTime
  const videoFrame = Math.floor((startTime + (frame / fps)) * fps);
  
  return (
    <AbsoluteFill>
      {/* Container do vídeo com efeetos */}
      <div
        style={{
          width: "100%",
          height: "100%",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          transform: `scale(${scale})`,
        }}
      >
        <div
          style={{
            width: "90%",
            height: "70%",
            borderRadius: 20,
            overflow: "hidden",
            boxShadow: `0 20px 40px rgba(0,0,0,0.3)`,
            border: `4px solid ${colors.accent}`,
            position: "relative",
          }}
        >
          {/* Vídeo principal */}
          <Video
            src={staticFile(videoPath)}
            startFrom={videoFrame}
            style={{
              width: "100%",
              height: "100%",
              objectFit: "cover",
            }}
            muted={false}
            volume={0.8}
          />
          
          {/* Overlay gradiente para melhor legibilidade */}
          <AbsoluteFill
            style={{
              background: `linear-gradient(transparent 60%, rgba(0,0,0,0.4) 100%)`,
              pointerEvents: "none",
            }}
          />
          
          {/* Efeito de brilho nas bordas */}
          <div
            style={{
              position: "absolute",
              top: -2,
              left: -2,
              right: -2,
              bottom: -2,
              borderRadius: 24,
              background: `linear-gradient(45deg, ${colors.primary}, ${colors.secondary}, ${colors.accent})`,
              zIndex: -1,
              opacity: 0.6,
            }}
          />
        </div>
      </div>
    </AbsoluteFill>
  );
};
