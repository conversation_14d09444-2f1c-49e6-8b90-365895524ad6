"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.preloadFont = void 0;
const preload_asset_1 = require("./preload-asset");
/*
 * @description Preloads a font so that when an <Img> tag is mounted, it can display immediately.
 * @see [Documentation](https://www.remotion.dev/docs/preload/preload-font)
 */
const preloadFont = (src) => {
    return (0, preload_asset_1.preloadAsset)(src, 'font');
};
exports.preloadFont = preloadFont;
