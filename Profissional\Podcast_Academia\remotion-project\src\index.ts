import { Composition, registerRoot } from "remotion";
import { InstagramReel } from "./InstagramReel";
import { InstagramStory } from "./InstagramStory";
import { InstagramPost } from "./InstagramPost";
import React from "react";

// Configurações para Instagram Reel (9:16)
export const INSTAGRAM_REEL_CONFIG = {
  id: "InstagramReel",
  component: InstagramReel,
  durationInFrames: 1800, // 60 segundos a 30fps
  fps: 30,
  width: 1080,
  height: 1920,
  props: {
    videoPath: "../Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4",
    startTime: 0,
    duration: 60,
    title: "Como construir seu primeiro Agente IA",
    subtitle: "Do ZERO em 90 dias",
    author: "<PERSON>",
    colors: {
      primary: "#FF6B35",
      secondary: "#2E86AB",
      accent: "#A23B72",
      background: "#F18F01",
      text: "#FFFFFF"
    }
  }
};

// Configurações para Instagram Story (9:16)
export const INSTAGRAM_STORY_CONFIG = {
  id: "InstagramStory",
  component: InstagramStory,
  durationInFrames: 450, // 15 segundos a 30fps
  fps: 30,
  width: 1080,
  height: 1920,
  props: {
    videoPath: "../Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4",
    startTime: 0,
    duration: 15,
    title: "Agente IA em 90 dias",
    author: "Sami",
    colors: {
      primary: "#FF6B35",
      secondary: "#2E86AB",
      accent: "#A23B72",
      background: "#F18F01",
      text: "#FFFFFF"
    }
  }
};

// Configurações para Instagram Post (1:1)
export const INSTAGRAM_POST_CONFIG = {
  id: "InstagramPost",
  component: InstagramPost,
  durationInFrames: 1800, // 60 segundos a 30fps
  fps: 30,
  width: 1080,
  height: 1080,
  props: {
    videoPath: "../Como construir seu primeiro Agente IA do ZERO em 90 dias ｜ Sami.mp4",
    startTime: 0,
    duration: 60,
    title: "Como construir seu primeiro Agente IA",
    author: "Sami",
    colors: {
      primary: "#FF6B35",
      secondary: "#2E86AB",
      accent: "#A23B72",
      background: "#F18F01",
      text: "#FFFFFF"
    }
  }
};

export const RemotionRoot: React.FC = () => {
  return (
    <>
      <Composition
        id={INSTAGRAM_REEL_CONFIG.id}
        component={INSTAGRAM_REEL_CONFIG.component}
        durationInFrames={INSTAGRAM_REEL_CONFIG.durationInFrames}
        fps={INSTAGRAM_REEL_CONFIG.fps}
        width={INSTAGRAM_REEL_CONFIG.width}
        height={INSTAGRAM_REEL_CONFIG.height}
        defaultProps={INSTAGRAM_REEL_CONFIG.props}
      />
      <Composition
        id={INSTAGRAM_STORY_CONFIG.id}
        component={INSTAGRAM_STORY_CONFIG.component}
        durationInFrames={INSTAGRAM_STORY_CONFIG.durationInFrames}
        fps={INSTAGRAM_STORY_CONFIG.fps}
        width={INSTAGRAM_STORY_CONFIG.width}
        height={INSTAGRAM_STORY_CONFIG.height}
        defaultProps={INSTAGRAM_STORY_CONFIG.props}
      />
      <Composition
        id={INSTAGRAM_POST_CONFIG.id}
        component={INSTAGRAM_POST_CONFIG.component}
        durationInFrames={INSTAGRAM_POST_CONFIG.durationInFrames}
        fps={INSTAGRAM_POST_CONFIG.fps}
        width={INSTAGRAM_POST_CONFIG.width}
        height={INSTAGRAM_POST_CONFIG.height}
        defaultProps={INSTAGRAM_POST_CONFIG.props}
      />
    </>
  );
};

registerRoot(RemotionRoot);
