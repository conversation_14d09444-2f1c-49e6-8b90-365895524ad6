export declare const HOST = "https://www.remotion.pro";
export type RegisterUsageEventResponse = {
    billable: boolean;
    classification: UsageEventClassification;
};
type UsageEventType = 'webcodec-conversion' | 'cloud-render';
export type UsageEventClassification = 'billable' | 'development' | 'failed';
export declare const registerUsageEvent: ({ apiKey, host, succeeded, event, }: {
    apiKey: string | null;
    host: string | null;
    succeeded: boolean;
    event: UsageEventType;
}) => Promise<RegisterUsageEventResponse>;
export {};
