import React, { useRef, useEffect, useState } from "react";
import {
  AbsoluteFill,
  Video,
  Audio,
  useCurrentFrame,
  useVideoConfig,
  interpolate,
  staticFile,
  Sequence,
  spring,
  Img,
  Easing,
} from "remotion";
import * as THREE from "three";

interface ViralReelUltimateProps {
  videoPath: string;
  audioPath: string;
  startTime: number;
  duration: number;
}

// Sistema de análise automática de fala integrado
const speechSegments = [
  {
    text: "💰 R$ 25 MIL",
    startTime: 0,
    endTime: 2.2,
    keywords: ["dinheiro", "valor"],
    intensity: "extreme",
    broll: "money",
    effects: ["shake", "zoom", "glow"]
  },
  {
    text: "em 90 dias",
    startTime: 2.2,
    endTime: 4.1,
    keywords: ["prazo", "tempo"],
    intensity: "high",
    effects: ["pulse"]
  },
  {
    text: "Como conseguir",
    startTime: 4.1,
    endTime: 6.3,
    keywords: ["método", "estratégia"],
    intensity: "medium",
    broll: "strategy",
    effects: ["slide"]
  },
  {
    text: "o primeiro cliente",
    startTime: 6.3,
    endTime: 8.7,
    keywords: ["cliente", "primeiro"],
    intensity: "extreme",
    broll: "handshake",
    effects: ["shake", "zoom", "highlight"]
  },
  {
    text: "e fechar um contrato",
    startTime: 8.7,
    endTime: 11.2,
    keywords: ["contrato", "negócio"],
    intensity: "high",
    broll: "contract",
    effects: ["zoom"]
  },
  {
    text: "de R$ 25 mil",
    startTime: 11.2,
    endTime: 13.8,
    keywords: ["dinheiro", "valor"],
    intensity: "extreme",
    broll: "money",
    effects: ["shake", "glow", "particles"]
  },
  {
    text: "usando IA",
    startTime: 13.8,
    endTime: 16.2,
    keywords: ["tecnologia", "IA"],
    intensity: "high",
    broll: "ai",
    effects: ["3d", "matrix"]
  },
  {
    text: "em apenas 90 dias",
    startTime: 16.2,
    endTime: 18.9,
    keywords: ["prazo", "rapidez"],
    intensity: "extreme",
    effects: ["countdown", "shake"]
  },
  {
    text: "sem experiência prévia",
    startTime: 18.9,
    endTime: 21.5,
    keywords: ["iniciante", "fácil"],
    intensity: "medium",
    effects: ["fade"]
  },
  {
    text: "Método comprovado",
    startTime: 21.5,
    endTime: 24.1,
    keywords: ["método", "prova"],
    intensity: "high",
    broll: "success",
    effects: ["stamp", "zoom"]
  },
  {
    text: "que funciona",
    startTime: 24.1,
    endTime: 26.8,
    keywords: ["funciona", "resultado"],
    intensity: "high",
    effects: ["checkmark", "pulse"]
  },
  {
    text: "para qualquer pessoa",
    startTime: 26.8,
    endTime: 29.4,
    keywords: ["universal", "todos"],
    intensity: "medium",
    broll: "people",
    effects: ["expand"]
  },
  {
    text: "mesmo sem conhecimento",
    startTime: 29.4,
    endTime: 32.1,
    keywords: ["iniciante", "simples"],
    intensity: "medium",
    effects: ["fade"]
  },
  {
    text: "técnico avançado",
    startTime: 32.1,
    endTime: 34.8,
    keywords: ["técnico", "avançado"],
    intensity: "low",
    effects: ["subtle"]
  },
  {
    text: "DESCUBRA O MÉTODO",
    startTime: 34.8,
    endTime: 37.5,
    keywords: ["descobrir", "método"],
    intensity: "extreme",
    broll: "revelation",
    effects: ["explosion", "shake", "glow"]
  },
  {
    text: "AGORA MESMO!",
    startTime: 37.5,
    endTime: 40.2,
    keywords: ["urgência", "agora"],
    intensity: "extreme",
    effects: ["urgent", "flash", "shake"]
  },
  {
    text: "Link na descrição",
    startTime: 40.2,
    endTime: 42.8,
    keywords: ["link", "descrição"],
    intensity: "high",
    effects: ["arrow", "pulse"]
  },
  {
    text: "👆 CLIQUE AQUI",
    startTime: 42.8,
    endTime: 45,
    keywords: ["clique", "ação"],
    intensity: "extreme",
    effects: ["button", "shake", "glow"]
  }
];

export const ViralReelUltimate: React.FC<ViralReelUltimateProps> = ({
  videoPath,
  audioPath,
  startTime,
  duration,
}) => {
  const frame = useCurrentFrame();
  const { durationInFrames, fps } = useVideoConfig();
  const time = frame / fps;
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [threeScene, setThreeScene] = useState<THREE.Scene | null>(null);

  // Encontrar o segmento atual baseado na análise de fala
  const currentSegment = speechSegments.find(
    segment => time >= segment.startTime && time < segment.endTime
  );

  // Sistema de intensidade avançado
  const getIntensityMultiplier = (intensity: string) => {
    switch(intensity) {
      case "extreme": return 4;
      case "high": return 2.5;
      case "medium": return 1.5;
      case "low": return 0.8;
      default: return 1;
    }
  };

  const intensityMultiplier = currentSegment ? getIntensityMultiplier(currentSegment.intensity) : 1;

  // Sistema de shake cinematográfico ultra-avançado
  const getCinematicShake = () => {
    if (!currentSegment || !currentSegment.effects.includes("shake")) {
      return { x: 0, y: 0, rotation: 0, scale: 1 };
    }

    const segmentProgress = (time - currentSegment.startTime) / (currentSegment.endTime - currentSegment.startTime);
    const shakeIntensity = intensityMultiplier * 2;
    
    // Múltiplas frequências para shake mais realista
    const freq1 = 0.8 + intensityMultiplier * 0.3;
    const freq2 = 1.2 + intensityMultiplier * 0.5;
    const freq3 = 0.5 + intensityMultiplier * 0.2;
    
    // Envelope de intensidade (começa forte, diminui)
    const envelope = interpolate(segmentProgress, [0, 0.3, 0.7, 1], [1, 0.8, 0.6, 0.2]);
    
    return {
      x: (Math.sin(time * freq1 * 10) + Math.sin(time * freq2 * 15) * 0.5) * shakeIntensity * envelope,
      y: (Math.cos(time * freq1 * 8) + Math.cos(time * freq3 * 12) * 0.3) * shakeIntensity * 0.6 * envelope,
      rotation: Math.sin(time * freq2 * 6) * shakeIntensity * 0.3 * envelope,
      scale: 1 + Math.sin(time * freq1 * 20) * 0.02 * intensityMultiplier * envelope
    };
  };

  // Sistema de zoom dinâmico baseado na intensidade
  const getDynamicZoom = () => {
    if (!currentSegment) return 1.0;
    
    const baseZoom = {
      "extreme": 1.4,
      "high": 1.25,
      "medium": 1.1,
      "low": 1.05
    }[currentSegment.intensity] || 1.0;
    
    const segmentProgress = (time - currentSegment.startTime) / (currentSegment.endTime - currentSegment.startTime);
    
    // Zoom com easing suave
    const zoomCurve = spring({
      frame: frame - (currentSegment.startTime * fps),
      fps,
      config: { damping: 200, stiffness: 100 }
    });
    
    // Micro-movimentos para zoom mais orgânico
    const microZoom = Math.sin(time * 3) * 0.02 * intensityMultiplier;
    
    return baseZoom * zoomCurve + microZoom;
  };

  // Sistema de efeitos 3D com Three.js
  useEffect(() => {
    if (!canvasRef.current) return;

    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, 16/9, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ 
      canvas: canvasRef.current,
      alpha: true,
      antialias: true 
    });

    renderer.setSize(1080, 1920);
    renderer.setClearColor(0x000000, 0);
    
    // Partículas para efeitos especiais
    const particleGeometry = new THREE.BufferGeometry();
    const particleCount = 100;
    const positions = new Float32Array(particleCount * 3);
    
    for (let i = 0; i < particleCount * 3; i++) {
      positions[i] = (Math.random() - 0.5) * 20;
    }
    
    particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    
    const particleMaterial = new THREE.PointsMaterial({
      color: 0xff6b35,
      size: 0.1,
      transparent: true,
      opacity: 0.8
    });
    
    const particles = new THREE.Points(particleGeometry, particleMaterial);
    scene.add(particles);
    
    camera.position.z = 5;
    setThreeScene(scene);

    return () => {
      renderer.dispose();
      particleGeometry.dispose();
      particleMaterial.dispose();
    };
  }, []);

  // Animação das partículas 3D
  useEffect(() => {
    if (!threeScene || !canvasRef.current) return;
    
    const renderer = new THREE.WebGLRenderer({ 
      canvas: canvasRef.current,
      alpha: true 
    });
    
    const camera = new THREE.PerspectiveCamera(75, 16/9, 0.1, 1000);
    camera.position.z = 5;
    
    // Animar partículas baseado na intensidade
    const particles = threeScene.children.find(child => child instanceof THREE.Points) as THREE.Points;
    
    if (particles && currentSegment?.effects.includes("particles")) {
      const positions = particles.geometry.attributes.position.array as Float32Array;
      
      for (let i = 0; i < positions.length; i += 3) {
        positions[i] += Math.sin(time * 2 + i) * 0.01 * intensityMultiplier;
        positions[i + 1] += Math.cos(time * 1.5 + i) * 0.01 * intensityMultiplier;
        positions[i + 2] += Math.sin(time * 3 + i) * 0.005 * intensityMultiplier;
      }
      
      particles.geometry.attributes.position.needsUpdate = true;
      particles.material.opacity = intensityMultiplier > 2 ? 0.8 : 0;
    }
    
    renderer.render(threeScene, camera);
  }, [time, currentSegment, intensityMultiplier, threeScene]);

  const shake = getCinematicShake();
  const zoom = getDynamicZoom();

  // Sistema de cores dinâmicas baseado na intensidade
  const getDynamicColors = () => {
    if (!currentSegment) return { primary: "#ff6b35", secondary: "#f7931e" };
    
    const colorSchemes = {
      "extreme": { primary: "#ff0040", secondary: "#ff6b35" },
      "high": { primary: "#ff6b35", secondary: "#f7931e" },
      "medium": { primary: "#f7931e", secondary: "#ffb347" },
      "low": { primary: "#ffb347", secondary: "#ffd700" }
    };
    
    return colorSchemes[currentSegment.intensity as keyof typeof colorSchemes] || colorSchemes.medium;
  };

  const colors = getDynamicColors();

  return (
    <AbsoluteFill>
      {/* Áudio sincronizado */}
      <Audio
        src={staticFile(audioPath)}
        startFrom={0}
        endAt={duration * fps}
        volume={1.0}
      />

      {/* Vídeo principal com efeitos avançados */}
      <div
        style={{
          width: "100%",
          height: "100%",
          background: "#000",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          overflow: "hidden",
        }}
      >
        <Video
          src={staticFile(videoPath)}
          startFrom={startTime * fps}
          endAt={(startTime + duration) * fps}
          style={{
            width: "100%",
            height: "133.33%",
            objectFit: "cover",
            objectPosition: "center",
            transform: `
              scale(${zoom * shake.scale}) 
              translateX(${shake.x}px) 
              translateY(${shake.y}px) 
              rotate(${shake.rotation}deg)
            `,
            filter: currentSegment?.effects.includes("glow") 
              ? `brightness(${1 + intensityMultiplier * 0.1}) contrast(${1 + intensityMultiplier * 0.2}) saturate(${1 + intensityMultiplier * 0.3})`
              : "none",
            transition: "all 0.1s ease-out",
          }}
          muted={true}
        />
      </div>

      {/* Sistema de B-roll tela cheia avançado - Temporariamente desabilitado */}
      {/* {currentSegment?.broll && (
        <BRollRenderer
          keyword={currentSegment.broll}
          startTime={currentSegment.startTime}
          duration={currentSegment.endTime - currentSegment.startTime}
          intensity={intensityMultiplier}
          effects={currentSegment.effects}
        />
      )} */}

      {/* Canvas 3D para efeitos especiais */}
      <canvas
        ref={canvasRef}
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          pointerEvents: "none",
          zIndex: 15,
          opacity: currentSegment?.effects.includes("particles") ? 0.8 : 0,
        }}
      />

      {/* Sistema de legendas ultra-avançado */}
      {currentSegment && (
        <AdvancedSubtitleRenderer
          segment={currentSegment}
          time={time}
          colors={colors}
          shake={shake}
          intensityMultiplier={intensityMultiplier}
        />
      )}

      {/* Efeitos visuais especiais */}
      {currentSegment?.effects.includes("flash") && (
        <AbsoluteFill
          style={{
            background: colors.primary,
            opacity: interpolate(
              Math.sin(frame * 0.8),
              [-1, 1],
              [0, 0.3 * intensityMultiplier]
            ),
            pointerEvents: "none",
            zIndex: 20,
          }}
        />
      )}

      {/* Progress bar viral */}
      <div
        style={{
          position: "absolute",
          bottom: "15px",
          left: "20px",
          right: "20px",
          height: "6px",
          background: "rgba(255,255,255,0.2)",
          borderRadius: "3px",
          overflow: "hidden",
          zIndex: 25,
        }}
      >
        <div
          style={{
            width: `${(time / duration) * 100}%`,
            height: "100%",
            background: `linear-gradient(90deg, ${colors.primary}, ${colors.secondary})`,
            borderRadius: "3px",
            boxShadow: `0 0 15px ${colors.primary}80`,
          }}
        />
      </div>

      {/* Contador de segmentos */}
      <div
        style={{
          position: "absolute",
          top: "25px",
          right: "25px",
          padding: "10px 18px",
          background: "rgba(0,0,0,0.8)",
          borderRadius: "25px",
          border: `2px solid ${colors.primary}`,
          backdropFilter: "blur(15px)",
          zIndex: 25,
        }}
      >
        <span
          style={{
            fontSize: "1rem",
            fontWeight: "700",
            color: "#fff",
            fontFamily: "'Inter', -apple-system, sans-serif",
          }}
        >
          {speechSegments.findIndex(s => s === currentSegment) + 1}/{speechSegments.length}
        </span>
      </div>
    </AbsoluteFill>
  );
};

// Componente de B-roll avançado
const BRollRenderer: React.FC<{
  keyword: string;
  startTime: number;
  duration: number;
  intensity: number;
  effects: string[];
}> = ({ keyword, startTime, duration, intensity, effects }) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  const time = frame / fps;
  
  const brollAssets = {
    money: "broll/money-stack-4k.mp4",
    handshake: "broll/handshake-professional.mp4",
    ai: "broll/ai-visualization.mp4",
    strategy: "broll/strategy-planning.mp4",
    contract: "broll/contract-signing.mp4",
    success: "broll/success-celebration.mp4",
    people: "broll/diverse-people.mp4",
    revelation: "broll/light-revelation.mp4"
  };

  const progress = (time - startTime) / duration;
  const opacity = interpolate(progress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);

  return (
    <AbsoluteFill
      style={{
        opacity,
        zIndex: 12,
        transform: effects.includes("3d") 
          ? `perspective(1000px) rotateY(${interpolate(progress, [0, 1], [-45, 0])}deg)`
          : undefined,
      }}
    >
      <Video
        src={staticFile(brollAssets[keyword as keyof typeof brollAssets] || brollAssets.money)}
        style={{
          width: "100%",
          height: "100%",
          objectFit: "cover",
          filter: `brightness(${1 + intensity * 0.1}) contrast(${1 + intensity * 0.2})`,
        }}
        muted
      />
      
      {/* Overlay de blend */}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `linear-gradient(45deg, rgba(255,107,53,${intensity * 0.1}), rgba(0,0,0,${intensity * 0.05}))`,
          mixBlendMode: "overlay",
        }}
      />
    </AbsoluteFill>
  );
};

// Componente de legendas ultra-avançado
const AdvancedSubtitleRenderer: React.FC<{
  segment: any;
  time: number;
  colors: any;
  shake: any;
  intensityMultiplier: number;
}> = ({ segment, time, colors, shake, intensityMultiplier }) => {
  const segmentProgress = (time - segment.startTime) / (segment.endTime - segment.startTime);
  
  const textScale = spring({
    frame: (time - segment.startTime) * 30,
    fps: 30,
    config: { damping: 200, stiffness: 100 }
  });

  const getTextStyle = () => {
    const baseSize = {
      "extreme": "4rem",
      "high": "3.2rem", 
      "medium": "2.6rem",
      "low": "2.2rem"
    }[segment.intensity] || "2.6rem";

    return {
      fontSize: baseSize,
      fontWeight: segment.intensity === "extreme" ? "900" : "800",
      color: "#fff",
      fontFamily: "'Inter', -apple-system, sans-serif",
      textShadow: `
        0 4px 20px rgba(0,0,0,0.9),
        0 0 30px ${colors.primary}80,
        0 0 60px ${colors.primary}40
      `,
      lineHeight: "1.1",
      letterSpacing: segment.intensity === "extreme" ? "-0.03em" : "-0.01em",
      textTransform: segment.intensity === "extreme" ? "uppercase" : "none",
    };
  };

  const getContainerStyle = () => {
    const isExtreme = segment.intensity === "extreme";
    const isCTA = segment.text.includes("CLIQUE") || segment.text.includes("DESCUBRA");
    
    return {
      background: isCTA 
        ? `linear-gradient(135deg, ${colors.primary}, ${colors.secondary})`
        : isExtreme
          ? `rgba(${colors.primary.slice(1).match(/.{2}/g)?.map(hex => parseInt(hex, 16)).join(',')}, 0.95)`
          : "rgba(0,0,0,0.85)",
      padding: isCTA ? "30px 45px" : isExtreme ? "25px 35px" : "20px 30px",
      borderRadius: isCTA ? "30px" : "20px",
      border: isExtreme ? `4px solid #fff` : `3px solid rgba(255,255,255,0.4)`,
      boxShadow: isExtreme 
        ? `0 20px 60px ${colors.primary}60, 0 0 100px ${colors.primary}40`
        : "0 15px 40px rgba(0,0,0,0.6)",
      backdropFilter: "blur(20px)",
    };
  };

  return (
    <div
      style={{
        position: "absolute",
        bottom: segment.text.includes("CLIQUE") ? "50%" : "30%",
        left: "50%",
        transform: `
          translate(-50%, ${segment.text.includes("CLIQUE") ? "50%" : "0"}) 
          scale(${textScale}) 
          translateX(${shake.x * 0.5}px) 
          translateY(${shake.y * 0.5}px)
        `,
        textAlign: "center",
        zIndex: 30,
        opacity: interpolate(segmentProgress, [0, 0.1, 0.9, 1], [0, 1, 1, 0]),
      }}
    >
      <div style={getContainerStyle()}>
        <span style={getTextStyle()}>
          {segment.text}
        </span>
        
        {/* Efeitos especiais para textos extremos */}
        {segment.intensity === "extreme" && (
          <>
            {/* Glow pulsante */}
            <div
              style={{
                position: "absolute",
                top: "-10px",
                left: "-10px",
                right: "-10px",
                bottom: "-10px",
                background: `linear-gradient(45deg, ${colors.primary}40, ${colors.secondary}40)`,
                borderRadius: "35px",
                opacity: interpolate(Math.sin(time * 8), [-1, 1], [0.3, 0.8]),
                zIndex: -1,
                filter: "blur(15px)",
              }}
            />
            
            {/* Partículas ao redor do texto */}
            {Array.from({ length: 8 }, (_, i) => (
              <div
                key={i}
                style={{
                  position: "absolute",
                  width: "6px",
                  height: "6px",
                  background: colors.primary,
                  borderRadius: "50%",
                  top: "50%",
                  left: "50%",
                  transform: `
                    translate(-50%, -50%) 
                    rotate(${i * 45 + time * 50}deg) 
                    translateX(${80 + Math.sin(time * 4) * 20}px)
                  `,
                  opacity: 0.8,
                  boxShadow: `0 0 10px ${colors.primary}`,
                }}
              />
            ))}
          </>
        )}
      </div>
    </div>
  );
};
