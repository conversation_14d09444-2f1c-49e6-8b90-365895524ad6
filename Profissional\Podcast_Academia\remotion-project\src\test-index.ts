import { Composition } from "remotion";
import React from "react";

// Componente de teste simples
const TestComponent: React.FC = () => {
  return (
    <div style={{ 
      backgroundColor: "red", 
      width: "100%", 
      height: "100%",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      color: "white",
      fontSize: "48px"
    }}>
      Test Component
    </div>
  );
};

// Configuração de teste
export const RemotionRoot: React.FC = () => {
  return (
    <>
      <Composition
        id="TestComposition"
        component={TestComponent}
        durationInFrames={150}
        fps={30}
        width={1080}
        height={1920}
      />
    </>
  );
};
