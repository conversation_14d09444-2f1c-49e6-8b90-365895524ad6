import type { DownloadMap } from './assets/download-map';
import type { PreprocessedAudioTrack } from './preprocess-audio-track';
export declare const createFfmpegComplexFilter: ({ filters, downloadMap, }: {
    filters: PreprocessedAudioTrack[];
    downloadMap: DownloadMap;
}) => Promise<{
    complexFilterFlag: [string, string] | null;
    cleanup: () => void;
    complexFilter: string | null;
}>;
