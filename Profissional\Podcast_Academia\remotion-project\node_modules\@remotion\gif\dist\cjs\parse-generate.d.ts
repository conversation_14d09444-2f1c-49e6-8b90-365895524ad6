import type { GifState } from './props';
export declare const parse: (src: string, { signal, }: {
    signal: AbortController["signal"];
}) => Promise<{
    loaded: boolean;
    delays: number[];
    frames: Uint8ClampedArray<ArrayBufferLike>[];
    width: number;
    height: number;
}>;
type ParserCallbackArgs = {
    width: number;
    height: number;
    delays: number[];
    frames: Uint8ClampedArray[];
};
export declare const generate: (info: ParserCallbackArgs) => GifState;
export {};
