import type { BrowserExecutable } from './browser-executable';
import type { B<PERSON>erLog } from './browser-log';
import type { HeadlessBrowser } from './browser/Browser';
import type { Page } from './browser/BrowserPage';
import type { LogLevel } from './log-level';
import type { ChromiumOptions } from './open-browser';
import type { ChromeMode } from './options/chrome-mode';
import type { OnBrowserDownload } from './options/on-browser-download';
export declare const getPageAndCleanupFn: ({ passedInInstance, browserExecutable, chromiumOptions, forceDeviceScaleFactor, indent, logLevel, onBrowserDownload, chromeMode, pageIndex, onBrowserLog, }: {
    passedInInstance: HeadlessBrowser | undefined;
    browserExecutable: BrowserExecutable | null;
    chromiumOptions: ChromiumOptions;
    indent: boolean;
    forceDeviceScaleFactor: number | undefined;
    logLevel: LogLevel;
    onBrowserDownload: OnBrowserDownload;
    chromeMode: ChromeMode;
    pageIndex: number;
    onBrowserLog: null | ((log: BrowserLog) => void);
}) => Promise<{
    cleanupPage: () => Promise<void>;
    page: Page;
}>;
