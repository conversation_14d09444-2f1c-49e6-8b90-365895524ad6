#!/usr/bin/env python3
"""
🎬 SISTEMA DE EDIÇÃO PROFISSIONAL PARA INSTAGRAM
Transforma vídeos de podcast em conteúdo viral e profissional
"""

import os
import sys
import subprocess
import json
from pathlib import Path
from datetime import datetime

# Instalar dependências necessárias
def install_requirements():
    """Instala as bibliotecas necessárias"""
    packages = [
        'moviepy',
        'speech_recognition',
        'pydub',
        'whisper',
        'openai-whisper',
        'pillow',
        'numpy'
    ]
    
    for package in packages:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} instalado com sucesso")
        except subprocess.CalledProcessError:
            print(f"❌ Erro ao instalar {package}")

class PodcastEditor:
    def __init__(self, input_dir, output_dir):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Configurações para Instagram
        self.instagram_configs = {
            'reel': {
                'resolution': (1080, 1920),  # 9:16
                'max_duration': 90,
                'min_duration': 15
            },
            'story': {
                'resolution': (1080, 1920),  # 9:16
                'max_duration': 15,
                'min_duration': 5
            },
            'post': {
                'resolution': (1080, 1080),  # 1:1
                'max_duration': 60,
                'min_duration': 3
            }
        }
    
    def get_video_info(self, video_path):
        """Extrai informações básicas do vídeo"""
        try:
            from moviepy.editor import VideoFileClip
            
            clip = VideoFileClip(str(video_path))
            info