// src/get-lottie-metadata.ts
var getLottieMetadata = (animationData) => {
  const width = animationData.w;
  const height = animationData.h;
  const framerate = animationData.fr;
  const durationInFrames = animationData.op;
  if (![width, height, framerate, durationInFrames].every(Boolean)) {
    return null;
  }
  return {
    durationInFrames: Math.floor(durationInFrames),
    durationInSeconds: durationInFrames / framerate,
    fps: framerate,
    height,
    width
  };
};
// src/Lottie.tsx
import lottie from "lottie-web";
import { useEffect, useRef, useState } from "react";
import { continueRender, delayRender, useCurrentFrame } from "remotion";

// src/utils.ts
var getLottieFrame = ({
  currentFrame,
  direction,
  loop,
  totalFrames
}) => {
  const nextFrame = loop ? currentFrame % totalFrames : Math.min(currentFrame, totalFrames - 1);
  if (direction === "backward") {
    return totalFrames - nextFrame;
  }
  return nextFrame;
};

// src/validate-loop.ts
var validateLoop = (loop) => {
  if (typeof loop === "undefined") {
    return;
  }
  if (typeof loop !== "boolean") {
    throw new TypeError(`The "loop" prop must be a boolean or undefined, but is "${JSON.stringify(loop)}"`);
  }
};

// src/validate-playbackrate.ts
var validatePlaybackRate = (playbackRate) => {
  if (typeof playbackRate === "undefined") {
    return;
  }
  if (typeof playbackRate !== "number") {
    throw new TypeError(`The "playbackRate" prop must be a number or undefined, but is ${JSON.stringify(playbackRate)}`);
  }
  if (Number.isNaN(playbackRate) || !Number.isFinite(playbackRate)) {
    throw new TypeError(`The "playbackRate" props must be a real number, but is ${playbackRate}`);
  }
  if (playbackRate <= 0) {
    throw new TypeError(`The "playbackRate" props must be positive, but is ${playbackRate}`);
  }
};

// src/Lottie.tsx
import { jsx } from "react/jsx-runtime";
var Lottie = ({
  animationData,
  className,
  direction,
  loop,
  playbackRate,
  style,
  onAnimationLoaded,
  renderer,
  preserveAspectRatio,
  assetsPath
}) => {
  if (typeof animationData !== "object") {
    throw new Error("animationData should be provided as an object. If you only have the path to the JSON file, load it and pass it as animationData. See https://remotion.dev/docs/lottie/lottie#example for more information.");
  }
  validatePlaybackRate(playbackRate);
  validateLoop(loop);
  const animationRef = useRef(null);
  const currentFrameRef = useRef(null);
  const containerRef = useRef(null);
  const onAnimationLoadedRef = useRef(onAnimationLoaded);
  onAnimationLoadedRef.current = onAnimationLoaded;
  const [handle] = useState(() => delayRender("Waiting for Lottie animation to load"));
  useEffect(() => {
    return () => {
      continueRender(handle);
    };
  }, [handle]);
  const frame = useCurrentFrame();
  currentFrameRef.current = frame;
  useEffect(() => {
    if (!containerRef.current) {
      return;
    }
    animationRef.current = lottie.loadAnimation({
      container: containerRef.current,
      autoplay: false,
      animationData,
      assetsPath: assetsPath ?? undefined,
      renderer: renderer ?? "svg",
      rendererSettings: {
        preserveAspectRatio: preserveAspectRatio ?? undefined
      }
    });
    const { current: animation } = animationRef;
    const onComplete = () => {
      if (currentFrameRef.current) {
        const frameToSet = getLottieFrame({
          currentFrame: currentFrameRef.current * (playbackRate ?? 1),
          direction,
          loop,
          totalFrames: animation.totalFrames
        });
        animationRef.current?.goToAndStop(Math.max(0, frameToSet - 1), true);
        animationRef.current?.goToAndStop(frameToSet, true);
      }
      continueRender(handle);
    };
    animation.addEventListener("DOMLoaded", onComplete);
    onAnimationLoadedRef.current?.(animation);
    return () => {
      animation.removeEventListener("DOMLoaded", onComplete);
      animation.destroy();
    };
  }, [
    animationData,
    assetsPath,
    direction,
    handle,
    loop,
    playbackRate,
    preserveAspectRatio,
    renderer
  ]);
  useEffect(() => {
    if (animationRef.current && direction) {
      animationRef.current.setDirection(direction === "backward" ? -1 : 1);
    }
  }, [direction]);
  useEffect(() => {
    if (animationRef.current && playbackRate) {
      animationRef.current.setSpeed(playbackRate);
    }
  }, [playbackRate]);
  useEffect(() => {
    if (!animationRef.current) {
      return;
    }
    const { totalFrames } = animationRef.current;
    const frameToSet = getLottieFrame({
      currentFrame: frame * (playbackRate ?? 1),
      direction,
      loop,
      totalFrames
    });
    animationRef.current.goToAndStop(frameToSet, true);
    const images = containerRef.current?.querySelectorAll("image");
    images.forEach((img) => {
      const currentHref = img.getAttributeNS("http://www.w3.org/1999/xlink", "href");
      if (currentHref && currentHref === img.href.baseVal) {
        return;
      }
      const imgHandle = delayRender(`Waiting for lottie image with src="${img.href.baseVal}" to load`);
      img.addEventListener("load", () => {
        continueRender(imgHandle);
      }, { once: true });
      img.setAttributeNS("http://www.w3.org/1999/xlink", "xlink:href", img.href.baseVal);
    });
  }, [direction, frame, loop, playbackRate]);
  return /* @__PURE__ */ jsx("div", {
    ref: containerRef,
    className,
    style
  });
};
export {
  getLottieMetadata,
  Lottie
};
