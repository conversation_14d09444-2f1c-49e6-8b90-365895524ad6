import React from "react";
import { MinimalistClip } from "./components/MinimalistClip";

interface InstagramStoryProps {
  videoPath: string;
  startTime: number;
  duration: number;
  scale: number;
  title?: string;
  subtitle?: string;
  author?: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
}

export const InstagramStory: React.FC<InstagramStoryProps> = (props) => {
  return (
    <MinimalistClip
      {...props}
    />
  );
};
