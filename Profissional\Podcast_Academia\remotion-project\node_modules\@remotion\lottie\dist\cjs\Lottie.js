"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Lottie = void 0;
const jsx_runtime_1 = require("react/jsx-runtime");
const lottie_web_1 = __importDefault(require("lottie-web"));
const react_1 = require("react");
const remotion_1 = require("remotion");
const utils_1 = require("./utils");
const validate_loop_1 = require("./validate-loop");
const validate_playbackrate_1 = require("./validate-playbackrate");
/**
 * @description	Part of the @remotion/lottie package.
 * @see [Documentation](https://www.remotion.dev/docs/lottie/lottie)
 */
const Lottie = ({ animationData, className, direction, loop, playbackRate, style, onAnimationLoaded, renderer, preserveAspectRatio, assetsPath, }) => {
    if (typeof animationData !== 'object') {
        throw new Error('animationData should be provided as an object. If you only have the path to the JSON file, load it and pass it as animationData. See https://remotion.dev/docs/lottie/lottie#example for more information.');
    }
    (0, validate_playbackrate_1.validatePlaybackRate)(playbackRate);
    (0, validate_loop_1.validateLoop)(loop);
    const animationRef = (0, react_1.useRef)(null);
    const currentFrameRef = (0, react_1.useRef)(null);
    const containerRef = (0, react_1.useRef)(null);
    const onAnimationLoadedRef = (0, react_1.useRef)(onAnimationLoaded);
    onAnimationLoadedRef.current = onAnimationLoaded;
    const [handle] = (0, react_1.useState)(() => (0, remotion_1.delayRender)('Waiting for Lottie animation to load'));
    // If component unmounts, continue the render
    (0, react_1.useEffect)(() => {
        return () => {
            (0, remotion_1.continueRender)(handle);
        };
    }, [handle]);
    const frame = (0, remotion_1.useCurrentFrame)();
    currentFrameRef.current = frame;
    (0, react_1.useEffect)(() => {
        var _a;
        if (!containerRef.current) {
            return;
        }
        animationRef.current = lottie_web_1.default.loadAnimation({
            container: containerRef.current,
            autoplay: false,
            animationData,
            assetsPath: assetsPath !== null && assetsPath !== void 0 ? assetsPath : undefined,
            renderer: renderer !== null && renderer !== void 0 ? renderer : 'svg',
            rendererSettings: {
                preserveAspectRatio: preserveAspectRatio !== null && preserveAspectRatio !== void 0 ? preserveAspectRatio : undefined,
            },
        });
        const { current: animation } = animationRef;
        const onComplete = () => {
            var _a, _b;
            // Seek frame twice to avoid Lottie initialization bug:
            // See LottieInitializationBugfix composition in the example project for a repro.
            // We can work around it by seeking twice, initially.
            if (currentFrameRef.current) {
                const frameToSet = (0, utils_1.getLottieFrame)({
                    currentFrame: currentFrameRef.current * (playbackRate !== null && playbackRate !== void 0 ? playbackRate : 1),
                    direction,
                    loop,
                    totalFrames: animation.totalFrames,
                });
                (_a = animationRef.current) === null || _a === void 0 ? void 0 : _a.goToAndStop(Math.max(0, frameToSet - 1), true);
                (_b = animationRef.current) === null || _b === void 0 ? void 0 : _b.goToAndStop(frameToSet, true);
            }
            (0, remotion_1.continueRender)(handle);
        };
        animation.addEventListener('DOMLoaded', onComplete);
        (_a = onAnimationLoadedRef.current) === null || _a === void 0 ? void 0 : _a.call(onAnimationLoadedRef, animation);
        return () => {
            animation.removeEventListener('DOMLoaded', onComplete);
            animation.destroy();
        };
    }, [
        animationData,
        assetsPath,
        direction,
        handle,
        loop,
        playbackRate,
        preserveAspectRatio,
        renderer,
    ]);
    (0, react_1.useEffect)(() => {
        if (animationRef.current && direction) {
            animationRef.current.setDirection(direction === 'backward' ? -1 : 1);
        }
    }, [direction]);
    (0, react_1.useEffect)(() => {
        if (animationRef.current && playbackRate) {
            animationRef.current.setSpeed(playbackRate);
        }
    }, [playbackRate]);
    (0, react_1.useEffect)(() => {
        var _a;
        if (!animationRef.current) {
            return;
        }
        const { totalFrames } = animationRef.current;
        const frameToSet = (0, utils_1.getLottieFrame)({
            currentFrame: frame * (playbackRate !== null && playbackRate !== void 0 ? playbackRate : 1),
            direction,
            loop,
            totalFrames,
        });
        animationRef.current.goToAndStop(frameToSet, true);
        const images = (_a = containerRef.current) === null || _a === void 0 ? void 0 : _a.querySelectorAll('image');
        images.forEach((img) => {
            const currentHref = img.getAttributeNS('http://www.w3.org/1999/xlink', 'href');
            if (currentHref && currentHref === img.href.baseVal) {
                return;
            }
            const imgHandle = (0, remotion_1.delayRender)(`Waiting for lottie image with src="${img.href.baseVal}" to load`);
            // https://stackoverflow.com/a/46839799
            img.addEventListener('load', () => {
                (0, remotion_1.continueRender)(imgHandle);
            }, { once: true });
            img.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', img.href.baseVal);
        });
    }, [direction, frame, loop, playbackRate]);
    return (0, jsx_runtime_1.jsx)("div", { ref: containerRef, className: className, style: style });
};
exports.Lottie = Lottie;
